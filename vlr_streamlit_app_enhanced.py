import streamlit as st
import requests
from bs4 import BeautifulSoup
import pandas as pd
import json
import time
from datetime import datetime
import re
from urllib.parse import urlparse
import io

# Configure Streamlit page
st.set_page_config(
    page_title="VLR.gg Event Scraper - Enhanced",
    page_icon="🎮",
    layout="wide",
    initial_sidebar_state="expanded"
)

class VLRStreamlitScraper:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

    def validate_url(self, url):
        """Validate VLR.gg event URL"""
        if not url:
            return False, "Please enter a URL"

        if not re.match(r'https?://www\.vlr\.gg/event/\d+/', url):
            return False, "Invalid VLR.gg event URL format. Expected: https://www.vlr.gg/event/{id}/{name}"

        try:
            response = requests.head(url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                return True, "Valid URL"
            else:
                return False, f"URL returned status {response.status_code}"
        except requests.RequestException as e:
            return False, f"Connection error: {str(e)}"

    def scrape_event_info(self, url):
        """Scrape basic event information"""
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        event_info = {
            'url': url,
            'scraped_at': datetime.now().isoformat()
        }

        # Event title
        title_elem = soup.find('h1', class_='wf-title')
        if title_elem:
            event_info['title'] = title_elem.get_text(strip=True)

        # Event description
        desc_elem = soup.find('div', class_='event-desc')
        if desc_elem:
            desc_text = desc_elem.get_text(strip=True)
            event_info['description'] = desc_text

            # Extract dates and location
            lines = desc_text.split('\n')
            for line in lines:
                line = line.strip()
                if any(month in line for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']):
                    event_info['dates'] = line
                elif any(word in line.lower() for word in ['korea', 'spain', 'china', 'usa', 'brazil']):
                    event_info['location'] = line

        # Prize pool
        prize_elem = soup.find('div', class_='prize-pool')
        if prize_elem:
            event_info['prize_pool'] = prize_elem.get_text(strip=True)

        return event_info

    def scrape_matches(self, matches_url):
        """Scrape all matches from the event"""
        response = requests.get(matches_url, headers=self.headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        matches = []
        match_containers = soup.find_all('a', class_='wf-module-item')

        for container in match_containers:
            match_data = self.extract_match_details(container)
            if match_data:
                matches.append(match_data)

        return matches

    def extract_match_details(self, container):
        """Extract match details from container"""
        try:
            # Get match URL
            match_url = 'https://www.vlr.gg' + container.get('href', '')

            # Extract team names
            team_elements = container.find_all('div', class_='match-item-vs-team-name')
            if len(team_elements) < 2:
                return None

            team1 = team_elements[0].get_text(strip=True)
            team2 = team_elements[1].get_text(strip=True)

            # Extract scores
            score_elements = container.find_all('div', class_='match-item-vs-team-score')
            score1 = score_elements[0].get_text(strip=True) if len(score_elements) > 0 else ''
            score2 = score_elements[1].get_text(strip=True) if len(score_elements) > 1 else ''

            # Extract stage
            event_elements = container.find_all('div', class_='match-item-event')
            stage = event_elements[0].get_text(strip=True) if event_elements else 'Unknown Stage'

            # Extract time
            time_elements = container.find_all('div', class_='match-item-time')
            match_time = time_elements[0].get_text(strip=True) if time_elements else 'Time TBD'

            # Determine status
            status = 'Completed' if score1 and score2 and score1.isdigit() and score2.isdigit() else 'Scheduled'

            return {
                'team1': team1,
                'team2': team2,
                'score1': score1,
                'score2': score2,
                'stage': stage,
                'time': match_time,
                'status': status,
                'match_url': match_url,
                'scraped_at': datetime.now().isoformat()
            }

        except Exception as e:
            st.error(f"Error extracting match details: {e}")
            return None

    def scrape_player_stats(self, matches, max_matches=5):
        """Scrape player statistics from completed matches"""
        player_stats = []
        completed_matches = [m for m in matches if m['status'] == 'Completed']

        # Limit matches to avoid long loading times
        matches_to_scrape = completed_matches[:max_matches]

        progress_bar = st.progress(0)
        status_text = st.empty()

        for i, match in enumerate(matches_to_scrape):
            status_text.text(f"Scraping player stats from match {i+1}/{len(matches_to_scrape)}: {match['team1']} vs {match['team2']}")

            try:
                match_stats = self.scrape_match_player_stats(match['match_url'])
                player_stats.extend(match_stats)

                progress_bar.progress((i + 1) / len(matches_to_scrape))
                time.sleep(1)  # Be respectful to the server

            except Exception as e:
                st.warning(f"Could not scrape stats for {match['team1']} vs {match['team2']}: {str(e)}")
                continue

        status_text.text("Player stats scraping completed!")
        return player_stats

    def scrape_match_player_stats(self, match_url):
        """Scrape player statistics from a specific match"""
        try:
            response = requests.get(match_url, headers=self.headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')
            player_stats = []

            # Look for player stats tables
            stats_tables = soup.find_all('table', class_='wf-table-inset')

            for table in stats_tables:
                rows = table.find_all('tr')

                for row in rows[1:]:  # Skip header row
                    cells = row.find_all('td')
                    if len(cells) >= 8:

                        # Extract player name
                        player_elem = cells[0].find('div', class_='text-of')
                        if not player_elem:
                            continue

                        player_name = player_elem.get_text(strip=True)

                        # Extract team
                        team_elem = row.find('img', class_='team-logo')
                        team = team_elem.get('alt', 'Unknown') if team_elem else 'Unknown'

                        # Extract stats
                        try:
                            stats = {
                                'player': player_name,
                                'team': team,
                                'match_url': match_url,
                                'acs': cells[1].get_text(strip=True) if len(cells) > 1 else '0',
                                'kills': cells[2].get_text(strip=True) if len(cells) > 2 else '0',
                                'deaths': cells[3].get_text(strip=True) if len(cells) > 3 else '0',
                                'assists': cells[4].get_text(strip=True) if len(cells) > 4 else '0',
                                'plus_minus': cells[5].get_text(strip=True) if len(cells) > 5 else '0',
                                'adr': cells[6].get_text(strip=True) if len(cells) > 6 else '0',
                                'hs_percent': cells[7].get_text(strip=True) if len(cells) > 7 else '0%',
                                'first_kills': cells[8].get_text(strip=True) if len(cells) > 8 else '0',
                                'first_deaths': cells[9].get_text(strip=True) if len(cells) > 9 else '0',
                                'scraped_at': datetime.now().isoformat()
                            }

                            # Calculate K/D ratio
                            try:
                                k = int(stats['kills'])
                                d = int(stats['deaths'])
                                stats['kd_ratio'] = f"{k/d:.2f}" if d > 0 else "∞"
                            except:
                                stats['kd_ratio'] = "0.00"

                            player_stats.append(stats)

                        except Exception as e:
                            continue

            return player_stats

        except Exception as e:
            return []

    def scrape_agents_data(self, matches, max_matches=5):
        """Scrape agent data from completed matches"""
        agents_data = []
        completed_matches = [m for m in matches if m['status'] == 'Completed']

        # Limit matches to avoid long loading times
        matches_to_scrape = completed_matches[:max_matches]

        progress_bar = st.progress(0)
        status_text = st.empty()

        for i, match in enumerate(matches_to_scrape):
            status_text.text(f"Scraping agent data from match {i+1}/{len(matches_to_scrape)}: {match['team1']} vs {match['team2']}")

            try:
                match_agents = self.scrape_match_agents(match['match_url'], match)
                agents_data.extend(match_agents)

                progress_bar.progress((i + 1) / len(matches_to_scrape))
                time.sleep(1)  # Be respectful to the server

            except Exception as e:
                st.warning(f"Could not scrape agents for {match['team1']} vs {match['team2']}: {str(e)}")
                continue

        status_text.text("Agent data scraping completed!")
        return agents_data

    def scrape_match_agents(self, match_url, match_info):
        """Scrape agent picks from a specific match"""
        try:
            response = requests.get(match_url, headers=self.headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')
            agents_data = []

            # Look for agent picks in the match page
            # VLR.gg shows agent picks in various ways, we'll try multiple selectors

            # Method 1: Look for agent images in player rows
            player_rows = soup.find_all('tr')

            for row in player_rows:
                # Look for agent images
                agent_imgs = row.find_all('img', class_='agent-image')
                if not agent_imgs:
                    agent_imgs = row.find_all('img', src=lambda x: x and 'agents' in x if x else False)

                if agent_imgs:
                    # Extract player name
                    player_elem = row.find('div', class_='text-of')
                    if not player_elem:
                        player_elem = row.find('a', class_='text-of')

                    if player_elem:
                        player_name = player_elem.get_text(strip=True)

                        # Extract team
                        team_elem = row.find('img', class_='team-logo')
                        team = team_elem.get('alt', 'Unknown') if team_elem else 'Unknown'

                        # Extract agent name from image
                        agent_img = agent_imgs[0]
                        agent_name = 'Unknown'

                        # Try to get agent name from alt text or src
                        if agent_img.get('alt'):
                            agent_name = agent_img.get('alt')
                        elif agent_img.get('src'):
                            src = agent_img.get('src')
                            # Extract agent name from URL like /img/vlr/game/agents/jett.png
                            if '/agents/' in src:
                                agent_name = src.split('/agents/')[-1].split('.')[0].title()

                        agents_data.append({
                            'player': player_name,
                            'team': team,
                            'agent': agent_name,
                            'match_url': match_url,
                            'team1': match_info['team1'],
                            'team2': match_info['team2'],
                            'stage': match_info['stage'],
                            'scraped_at': datetime.now().isoformat()
                        })

            # Method 2: Look for composition sections if Method 1 didn't work
            if not agents_data:
                comp_sections = soup.find_all('div', class_='composition')
                for section in comp_sections:
                    agent_imgs = section.find_all('img')
                    for img in agent_imgs:
                        if 'agents' in img.get('src', ''):
                            agent_name = img.get('alt', 'Unknown')
                            if agent_name == 'Unknown' and img.get('src'):
                                src = img.get('src')
                                if '/agents/' in src:
                                    agent_name = src.split('/agents/')[-1].split('.')[0].title()

                            agents_data.append({
                                'player': 'Unknown',
                                'team': 'Unknown',
                                'agent': agent_name,
                                'match_url': match_url,
                                'team1': match_info['team1'],
                                'team2': match_info['team2'],
                                'stage': match_info['stage'],
                                'scraped_at': datetime.now().isoformat()
                            })

            return agents_data

        except Exception as e:
            return []

def calculate_team_stats(player_stats):
    """Calculate team statistics from player data"""
    team_stats = {}

    for player in player_stats:
        team = player['team']
        if team not in team_stats:
            team_stats[team] = {
                'team': team,
                'total_players': 0,
                'total_kills': 0,
                'total_deaths': 0,
                'total_assists': 0,
                'total_acs': 0,
                'matches_played': set()
            }

        try:
            team_stats[team]['total_players'] += 1
            team_stats[team]['total_kills'] += int(player['kills'])
            team_stats[team]['total_deaths'] += int(player['deaths'])
            team_stats[team]['total_assists'] += int(player['assists'])
            team_stats[team]['total_acs'] += int(player['acs'])
            team_stats[team]['matches_played'].add(player['match_url'])
        except ValueError:
            continue

    # Calculate averages
    for team, stats in team_stats.items():
        matches_count = len(stats['matches_played'])
        if matches_count > 0:
            stats['avg_kills_per_match'] = stats['total_kills'] / matches_count
            stats['avg_deaths_per_match'] = stats['total_deaths'] / matches_count
            stats['avg_acs_per_match'] = stats['total_acs'] / matches_count
            stats['kd_ratio'] = stats['total_kills'] / stats['total_deaths'] if stats['total_deaths'] > 0 else float('inf')

        # Convert set to count
        stats['matches_played'] = matches_count

    return list(team_stats.values())

def calculate_agent_stats(agents_data):
    """Calculate agent statistics from agent data"""
    agent_stats = {}

    for agent_pick in agents_data:
        agent = agent_pick['agent']
        if agent not in agent_stats:
            agent_stats[agent] = {
                'agent': agent,
                'total_picks': 0,
                'teams_used': set(),
                'players_used': set(),
                'matches_played': set()
            }

        agent_stats[agent]['total_picks'] += 1
        agent_stats[agent]['teams_used'].add(agent_pick['team'])
        agent_stats[agent]['players_used'].add(agent_pick['player'])
        agent_stats[agent]['matches_played'].add(agent_pick['match_url'])

    # Convert sets to counts and calculate percentages
    total_picks = sum(stats['total_picks'] for stats in agent_stats.values())

    for agent, stats in agent_stats.items():
        stats['teams_count'] = len(stats['teams_used'])
        stats['players_count'] = len(stats['players_used'])
        stats['matches_count'] = len(stats['matches_played'])
        stats['pick_rate'] = f"{(stats['total_picks'] / total_picks * 100):.1f}%" if total_picks > 0 else "0.0%"

        # Remove sets (not JSON serializable)
        del stats['teams_used']
        del stats['players_used']
        del stats['matches_played']

    return list(agent_stats.values())

def save_separate_json_files(data, base_filename):
    """Save data into 3 separate JSON files"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    files_created = []

    # 1. Save matches data
    if data.get('matches'):
        matches_filename = f"matches_{base_filename}_{timestamp}.json"
        matches_data = {
            'event_info': data.get('event_info', {}),
            'matches': data['matches'],
            'scraped_at': datetime.now().isoformat(),
            'total_matches': len(data['matches'])
        }

        matches_json = json.dumps(matches_data, indent=2, ensure_ascii=False)
        files_created.append({
            'name': matches_filename,
            'data': matches_json,
            'type': 'matches'
        })

    # 2. Save player stats data
    if data.get('player_stats'):
        players_filename = f"player_stats_{base_filename}_{timestamp}.json"
        players_data = {
            'event_info': data.get('event_info', {}),
            'player_stats': data['player_stats'],
            'team_stats': data.get('team_stats', []),
            'scraped_at': datetime.now().isoformat(),
            'total_players': len(data['player_stats'])
        }

        players_json = json.dumps(players_data, indent=2, ensure_ascii=False)
        files_created.append({
            'name': players_filename,
            'data': players_json,
            'type': 'player_stats'
        })

    # 3. Save agents data
    if data.get('agents_data'):
        agents_filename = f"agents_{base_filename}_{timestamp}.json"
        agents_data = {
            'event_info': data.get('event_info', {}),
            'agents_data': data['agents_data'],
            'agent_stats': data.get('agent_stats', []),
            'scraped_at': datetime.now().isoformat(),
            'total_agent_picks': len(data['agents_data'])
        }

        agents_json = json.dumps(agents_data, indent=2, ensure_ascii=False)
        files_created.append({
            'name': agents_filename,
            'data': agents_json,
            'type': 'agents'
        })

    return files_created

def main():
    st.title("🎮 VLR.gg Event Data Scraper - Enhanced")
    st.markdown("### Extract comprehensive data from VLR.gg: Match Scores, Player Stats & Agent Picks")

    scraper = VLRStreamlitScraper()

    # Initialize session state
    if 'scraped_data' not in st.session_state:
        st.session_state.scraped_data = None
    if 'data_confirmed' not in st.session_state:
        st.session_state.data_confirmed = False
    if 'scraping_progress' not in st.session_state:
        st.session_state.scraping_progress = 0
    if 'scraping_status' not in st.session_state:
        st.session_state.scraping_status = "Ready to scrape..."

    # 1. URL Input Section
    st.header("📝 Event URL Input")
    with st.container():
        col1, col2 = st.columns([3, 1])

        with col1:
            url = st.text_input(
                "VLR.gg Event URL:",
                placeholder="https://www.vlr.gg/event/2097/valorant-champions-2024",
                help="Enter a VLR.gg event URL in the format: https://www.vlr.gg/event/{id}/{name}",
                key="url_input"
            )

        with col2:
            st.write("")  # Spacing
            validate_clicked = st.button("🔍 Validate URL", type="secondary")

        # URL validation feedback
        if validate_clicked and url:
            is_valid, message = scraper.validate_url(url)
            if is_valid:
                st.success(f"✅ {message} - Ready to scrape")
            else:
                st.error(f"❌ {message}")
        elif validate_clicked:
            st.warning("Please enter a URL first")

    st.divider()

    # 2. Control Buttons Section
    st.header("🎛️ Control Panel")
    with st.container():
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            # Scraping options in expander
            with st.expander("⚙️ Scraping Options"):
                include_player_stats = st.checkbox("Include Player Statistics", value=True,
                                                 help="Scrape detailed player stats (takes longer)")
                include_agents_data = st.checkbox("Include Agent Data", value=True,
                                                help="Scrape agent picks from matches (takes longer)")
                max_matches = st.slider("Max matches for detailed data", 1, 20, 5,
                                       help="Limit player stats and agent data to first N completed matches")

        with col2:
            st.write("")  # Spacing
            scrape_clicked = st.button("🚀 Start Scraping", type="primary", disabled=not url)

        with col3:
            st.write("")  # Spacing
            if st.button("🗑️ Clear Data", type="secondary"):
                st.session_state.scraped_data = None
                st.session_state.data_confirmed = False
                st.session_state.scraping_progress = 0
                st.session_state.scraping_status = "Data cleared - Ready to scrape..."
                st.rerun()

        with col4:
            st.write("")  # Spacing
            if st.session_state.scraped_data and not st.session_state.data_confirmed:
                if st.button("⏹️ Stop", type="secondary"):
                    st.session_state.scraping_status = "Scraping stopped by user"

    st.divider()

    # 3. Progress Section
    st.header("📊 Scraping Progress")
    with st.container():
        # Progress bar
        progress_bar = st.progress(st.session_state.scraping_progress / 100)

        # Status text
        st.text(st.session_state.scraping_status)

        # Statistics
        if st.session_state.scraped_data:
            data = st.session_state.scraped_data
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("🏆 Total Matches", len(data['matches']))
            with col2:
                st.metric("👤 Player Records", len(data['player_stats']))
            with col3:
                st.metric("🎭 Agent Picks", len(data.get('agents_data', [])))
            with col4:
                unique_teams = len(set([m['team1'] for m in data['matches']] + [m['team2'] for m in data['matches']]))
                st.metric("🏅 Teams", unique_teams)

    # Handle scraping
    if scrape_clicked and url:
        is_valid, message = scraper.validate_url(url)
        if is_valid:
            # Update progress
            st.session_state.scraping_progress = 10
            st.session_state.scraping_status = "Starting scraper..."

            try:
                # Scrape event info
                st.session_state.scraping_status = "Fetching event information..."
                st.session_state.scraping_progress = 20
                event_info = scraper.scrape_event_info(url)

                # Scrape matches
                st.session_state.scraping_status = "Fetching matches..."
                st.session_state.scraping_progress = 40
                matches_url = url.replace('/event/', '/event/matches/')
                matches = scraper.scrape_matches(matches_url)

                # Scrape player stats if requested
                player_stats = []
                if include_player_stats and matches:
                    st.session_state.scraping_status = "Fetching player statistics..."
                    st.session_state.scraping_progress = 50
                    player_stats = scraper.scrape_player_stats(matches, max_matches)

                # Scrape agent data if requested
                agents_data = []
                if include_agents_data and matches:
                    st.session_state.scraping_status = "Fetching agent data..."
                    st.session_state.scraping_progress = 70
                    agents_data = scraper.scrape_agents_data(matches, max_matches)

                # Calculate team stats
                st.session_state.scraping_status = "Calculating team statistics..."
                st.session_state.scraping_progress = 85
                team_stats = calculate_team_stats(player_stats)

                # Calculate agent stats
                st.session_state.scraping_status = "Calculating agent statistics..."
                st.session_state.scraping_progress = 95
                agent_stats = calculate_agent_stats(agents_data)

                # Store data in session state
                st.session_state.scraped_data = {
                    'event_info': event_info,
                    'matches': matches,
                    'player_stats': player_stats,
                    'team_stats': team_stats,
                    'agents_data': agents_data,
                    'agent_stats': agent_stats,
                    'scraping_options': {
                        'include_player_stats': include_player_stats,
                        'include_agents_data': include_agents_data,
                        'max_matches': max_matches
                    }
                }
                st.session_state.data_confirmed = False
                st.session_state.scraping_progress = 100
                st.session_state.scraping_status = "✅ Scraping completed successfully!"

                st.success("✅ Data scraped successfully!")
                st.rerun()

            except Exception as e:
                st.session_state.scraping_status = f"❌ Error during scraping: {str(e)}"
                st.error(f"❌ Error during scraping: {str(e)}")
        else:
            st.error(f"❌ {message}")

    st.divider()

    # 4. Results Display Section - 3 separate tabs as requested
    if st.session_state.scraped_data:
        st.header("📊 Data Overview - 3 Separate Tabs")

        # Create 3 main tabs for the different data types
        tab1, tab2, tab3 = st.tabs(["🏆 Matches Data", "👤 Player Stats", "🎭 Agents Data"])

        data = st.session_state.scraped_data

        # Tab 1: Matches Data (scores, teams, dates, etc.)
        with tab1:
            st.subheader("🏆 Match Scores, Teams & Dates")

            # Event Info Summary
            event_info = data['event_info']
            with st.container(border=True):
                col1, col2 = st.columns(2)
                with col1:
                    if 'title' in event_info:
                        st.metric("📋 Event Title", event_info['title'])
                    if 'dates' in event_info:
                        st.metric("📅 Dates", event_info['dates'])

                with col2:
                    if 'location' in event_info:
                        st.metric("📍 Location", event_info['location'])
                    if 'prize_pool' in event_info:
                        st.metric("💰 Prize Pool", event_info['prize_pool'])

            # Matches Data
            matches = data['matches']
            if matches:
                # Convert to DataFrame for better display
                matches_df = pd.DataFrame(matches)

                # Display summary metrics
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("📊 Total Matches", len(matches))
                with col2:
                    completed = len([m for m in matches if m['status'] == 'Completed'])
                    st.metric("✅ Completed", completed)
                with col3:
                    scheduled = len([m for m in matches if m['status'] == 'Scheduled'])
                    st.metric("⏰ Scheduled", scheduled)
                with col4:
                    unique_teams = len(set([m['team1'] for m in matches] + [m['team2'] for m in matches]))
                    st.metric("🏅 Teams", unique_teams)

                # Display matches table
                st.subheader("📋 Matches Table")
                display_columns = ['team1', 'score1', 'score2', 'team2', 'stage', 'time', 'status']
                st.dataframe(
                    matches_df[display_columns],
                    use_container_width=True,
                    hide_index=True
                )
            else:
                st.warning("No matches found")

        # Tab 2: Player Statistics
        with tab2:
            st.subheader("👤 Player Statistics from Stats Tab")

            player_stats = data.get('player_stats', [])
            if player_stats:
                # Convert to DataFrame
                players_df = pd.DataFrame(player_stats)

                # Display summary
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("👥 Total Players", len(player_stats))
                with col2:
                    unique_teams = len(set([p['team'] for p in player_stats]))
                    st.metric("🏅 Teams", unique_teams)
                with col3:
                    matches_analyzed = len(set([p['match_url'] for p in player_stats]))
                    st.metric("🏆 Matches Analyzed", matches_analyzed)

                # Display top performers
                st.subheader("🌟 Top Performers")

                # Convert numeric columns
                numeric_cols = ['acs', 'kills', 'deaths', 'assists', 'adr']
                for col in numeric_cols:
                    players_df[col] = pd.to_numeric(players_df[col], errors='coerce')

                # Top ACS players
                top_acs = players_df.nlargest(5, 'acs')[['player', 'team', 'acs', 'kills', 'deaths', 'kd_ratio']]
                st.write("**🎯 Top ACS Players:**")
                st.dataframe(top_acs, hide_index=True)

                # Full player stats table
                st.subheader("📊 All Player Statistics")
                display_cols = ['player', 'team', 'acs', 'kills', 'deaths', 'assists', 'kd_ratio', 'adr', 'hs_percent']
                st.dataframe(
                    players_df[display_cols],
                    use_container_width=True,
                    hide_index=True
                )
            else:
                st.warning("No player statistics found. Enable 'Include Player Statistics' in scraping options.")

        # Tab 3: Agents Data
        with tab3:
            st.subheader("🎭 Agent Data from Agents Tab")

            agents_data = data.get('agents_data', [])
            agent_stats = data.get('agent_stats', [])

            if agents_data:
                # Convert to DataFrame
                agents_df = pd.DataFrame(agents_data)

                # Display summary
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("🎭 Total Agent Picks", len(agents_data))
                with col2:
                    unique_agents = len(set([a['agent'] for a in agents_data]))
                    st.metric("🎮 Unique Agents", unique_agents)
                with col3:
                    matches_with_agents = len(set([a['match_url'] for a in agents_data]))
                    st.metric("🏆 Matches with Agent Data", matches_with_agents)

                # Agent Statistics
                if agent_stats:
                    st.subheader("📊 Agent Pick Statistics")
                    agent_stats_df = pd.DataFrame(agent_stats)

                    # Sort by total picks
                    agent_stats_df = agent_stats_df.sort_values('total_picks', ascending=False)

                    # Display agent stats
                    st.dataframe(
                        agent_stats_df[['agent', 'total_picks', 'pick_rate', 'teams_count', 'players_count']],
                        use_container_width=True,
                        hide_index=True,
                        column_config={
                            'agent': 'Agent',
                            'total_picks': 'Total Picks',
                            'pick_rate': 'Pick Rate',
                            'teams_count': 'Teams Used',
                            'players_count': 'Players Used'
                        }
                    )

                # Raw agent picks data
                st.subheader("🎯 All Agent Picks")
                display_cols = ['player', 'team', 'agent', 'team1', 'team2', 'stage']
                st.dataframe(
                    agents_df[display_cols],
                    use_container_width=True,
                    hide_index=True
                )
            else:
                st.warning("No agent data found. Enable 'Include Agent Data' in scraping options.")

        # Save Section
        st.header("💾 Save Data as Separate JSON Files")

        if not st.session_state.data_confirmed:
            st.warning("⚠️ Please review the data above and confirm it's correct before saving.")

            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ Confirm Data is Correct", type="primary"):
                    st.session_state.data_confirmed = True
                    st.success("✅ Data confirmed! You can now save it.")
                    st.rerun()

            with col2:
                if st.button("❌ Data is Incorrect", type="secondary"):
                    st.session_state.scraped_data = None
                    st.session_state.data_confirmed = False
                    st.info("Data cleared. Please scrape again.")
                    st.rerun()

        else:
            st.success("✅ Data confirmed and ready to save!")

            # Generate separate JSON files
            event_name = data.get('event_info', {}).get('title', 'vlr_event').replace(' ', '_').lower()
            json_files = save_separate_json_files(data, event_name)

            # Display download buttons for each file type
            col1, col2, col3 = st.columns(3)

            for i, file_info in enumerate(json_files):
                with [col1, col2, col3][i % 3]:
                    file_type_emoji = {"matches": "🏆", "player_stats": "👤", "agents": "🎭"}
                    emoji = file_type_emoji.get(file_info['type'], "📄")

                    st.download_button(
                        label=f"{emoji} Download {file_info['type'].replace('_', ' ').title()} JSON",
                        data=file_info['data'],
                        file_name=file_info['name'],
                        mime="application/json"
                    )

            # Reset button
            st.divider()
            if st.button("🔄 Start New Scraping Session", type="secondary"):
                st.session_state.scraped_data = None
                st.session_state.data_confirmed = False
                st.info("Session reset. You can now scrape new data.")
                st.rerun()

    else:
        # Welcome message when no data is loaded
        st.info("👈 Enter a VLR.gg event URL above and click 'Start Scraping' to begin!")

        # Example URLs
        st.subheader("📝 Example URLs")
        st.code("https://www.vlr.gg/event/2097/valorant-champions-2024")
        st.code("https://www.vlr.gg/event/1921/champions-tour-2024-masters-madrid")
        st.code("https://www.vlr.gg/event/1999/champions-tour-2024-masters-shanghai")

if __name__ == "__main__":
    main()