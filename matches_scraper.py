import requests
from bs4 import BeautifulSoup
import time
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

class MatchesScraper:
    """
    Dedicated scraper for VLR.gg matches data
    Handles match details, scores, series information, and team performance
    """
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def construct_matches_url(self, main_url: str) -> str:
        """
        Construct matches URL from main event URL
        Example: https://www.vlr.gg/event/2097/valorant-champions-2024
        -> https://www.vlr.gg/event/matches/2097/valorant-champions-2024
        """
        try:
            # Extract event ID
            match = re.search(r'/event/(\d+)/', main_url)
            if not match:
                raise ValueError("Could not extract event ID from URL")
            
            event_id = match.group(1)
            
            # Extract event name from URL
            url_parts = main_url.split('/')
            event_name = url_parts[-1] if url_parts[-1] else url_parts[-2]
            
            matches_url = f"https://www.vlr.gg/event/matches/{event_id}/{event_name}"
            return matches_url
            
        except Exception as e:
            raise ValueError(f"Error constructing matches URL: {e}")
    
    def scrape_matches(self, main_url: str, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Scrape all matches data from the matches tab
        """
        try:
            matches_url = self.construct_matches_url(main_url)
            
            if progress_callback:
                progress_callback("Fetching matches page...")
            
            response = self.session.get(matches_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            if progress_callback:
                progress_callback("Parsing matches data...")
            
            # Extract matches
            matches = self._extract_matches(soup, progress_callback)
            
            # Extract series information
            series_info = self._extract_series_info(soup)
            
            # Extract tournament bracket info if available
            bracket_info = self._extract_bracket_info(soup)
            
            result = {
                'matches': matches,
                'series_info': series_info,
                'bracket_info': bracket_info,
                'total_matches': len(matches),
                'scraped_from': matches_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            if progress_callback:
                progress_callback(f"Completed! Found {len(matches)} matches")
            
            return result
            
        except Exception as e:
            raise Exception(f"Error scraping matches: {e}")
    
    def _extract_matches(self, soup: BeautifulSoup, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """Extract individual match data"""
        matches = []
        
        # Find all match containers
        match_containers = soup.find_all('a', class_='wf-module-item')
        
        if not match_containers:
            # Try alternative selectors
            match_containers = soup.find_all('div', class_='match-item')
            if not match_containers:
                match_containers = soup.find_all('a', href=re.compile(r'/\d+/'))
        
        for i, container in enumerate(match_containers):
            if progress_callback and i % 10 == 0:
                progress_callback(f"Processing match {i+1}/{len(match_containers)}")
            
            match_data = self._extract_single_match(container)
            if match_data:
                matches.append(match_data)
            
            # Small delay to be respectful
            time.sleep(0.1)
        
        return matches
    
    def _extract_single_match(self, container) -> Optional[Dict[str, Any]]:
        """Extract data from a single match container"""
        try:
            match_data = {
                'scraped_at': datetime.now().isoformat()
            }
            
            # Get match URL
            href = container.get('href', '')
            if href:
                match_data['match_url'] = 'https://www.vlr.gg' + href if href.startswith('/') else href
            
            # Extract team names
            team_elements = container.find_all(['div', 'span'], class_=re.compile(r'team.*name|match.*team'))
            if len(team_elements) >= 2:
                match_data['team1'] = team_elements[0].get_text(strip=True)
                match_data['team2'] = team_elements[1].get_text(strip=True)
            else:
                # Alternative extraction method
                team_links = container.find_all('a', href=re.compile(r'/team/'))
                if len(team_links) >= 2:
                    match_data['team1'] = team_links[0].get_text(strip=True)
                    match_data['team2'] = team_links[1].get_text(strip=True)
            
            # Extract scores
            score_elements = container.find_all(['div', 'span'], class_=re.compile(r'score|match.*score'))
            if len(score_elements) >= 2:
                match_data['score1'] = score_elements[0].get_text(strip=True)
                match_data['score2'] = score_elements[1].get_text(strip=True)
            else:
                # Try to find scores in text
                score_text = container.get_text()
                score_matches = re.findall(r'\b(\d+)\s*[-:]\s*(\d+)\b', score_text)
                if score_matches:
                    match_data['score1'] = score_matches[0][0]
                    match_data['score2'] = score_matches[0][1]
            
            # Extract stage/round information
            stage_elements = container.find_all(['div', 'span'], class_=re.compile(r'stage|round|event'))
            if stage_elements:
                match_data['stage'] = stage_elements[0].get_text(strip=True)
            
            # Extract time information
            time_elements = container.find_all(['div', 'span'], class_=re.compile(r'time|date'))
            if time_elements:
                match_data['time'] = time_elements[0].get_text(strip=True)
            
            # Extract series ID if available
            if href:
                series_match = re.search(r'series_id=(\d+)', href)
                if series_match:
                    match_data['series_id'] = series_match.group(1)
            
            # Determine match status and winner
            if 'score1' in match_data and 'score2' in match_data:
                s1, s2 = match_data['score1'], match_data['score2']
                if s1.isdigit() and s2.isdigit():
                    match_data['status'] = 'Completed'
                    s1_int, s2_int = int(s1), int(s2)
                    if s1_int > s2_int:
                        match_data['winner'] = match_data.get('team1', '')
                    elif s2_int > s1_int:
                        match_data['winner'] = match_data.get('team2', '')
                    else:
                        match_data['winner'] = 'Draw'
                else:
                    match_data['status'] = 'Scheduled'
            else:
                match_data['status'] = 'Scheduled'
            
            # Only return if we have essential data
            if 'team1' in match_data and 'team2' in match_data:
                return match_data
            
            return None
            
        except Exception as e:
            return None
    
    def _extract_series_info(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract series information"""
        try:
            series_info = []
            
            # Look for series containers
            series_containers = soup.find_all(['div'], class_=re.compile(r'series|bracket'))
            
            for container in series_containers:
                series_data = {}
                
                # Extract series title
                title_elem = container.find(['h1', 'h2', 'h3', 'div'], class_=re.compile(r'title|name'))
                if title_elem:
                    series_data['title'] = title_elem.get_text(strip=True)
                
                # Count matches in this series
                series_matches = container.find_all('a', class_='wf-module-item')
                series_data['matches_count'] = len(series_matches)
                
                if series_data and series_data.get('title'):
                    series_info.append(series_data)
            
            return series_info
            
        except Exception:
            return []
    
    def _extract_bracket_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract tournament bracket information"""
        try:
            bracket_info = {
                'stages': [],
                'format': 'Unknown'
            }
            
            # Look for bracket stages
            stage_elements = soup.find_all(['div'], class_=re.compile(r'stage|round|bracket'))
            
            for stage_elem in stage_elements:
                stage_text = stage_elem.get_text(strip=True)
                if stage_text and len(stage_text) < 100:  # Avoid long descriptions
                    bracket_info['stages'].append(stage_text)
            
            # Try to determine tournament format
            page_text = soup.get_text().lower()
            if 'single elimination' in page_text:
                bracket_info['format'] = 'Single Elimination'
            elif 'double elimination' in page_text:
                bracket_info['format'] = 'Double Elimination'
            elif 'round robin' in page_text:
                bracket_info['format'] = 'Round Robin'
            elif 'swiss' in page_text:
                bracket_info['format'] = 'Swiss'
            
            return bracket_info
            
        except Exception:
            return {'stages': [], 'format': 'Unknown'}
    
    def get_team_performance(self, matches_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Calculate team performance statistics from matches data"""
        try:
            matches = matches_data.get('matches', [])
            team_stats = {}
            
            for match in matches:
                if match.get('status') != 'Completed':
                    continue
                
                team1 = match.get('team1', '')
                team2 = match.get('team2', '')
                winner = match.get('winner', '')
                
                # Initialize team stats
                for team in [team1, team2]:
                    if team and team not in team_stats:
                        team_stats[team] = {
                            'matches_played': 0,
                            'wins': 0,
                            'losses': 0,
                            'win_rate': 0.0
                        }
                
                # Update stats
                if team1 in team_stats:
                    team_stats[team1]['matches_played'] += 1
                    if winner == team1:
                        team_stats[team1]['wins'] += 1
                    else:
                        team_stats[team1]['losses'] += 1
                
                if team2 in team_stats:
                    team_stats[team2]['matches_played'] += 1
                    if winner == team2:
                        team_stats[team2]['wins'] += 1
                    else:
                        team_stats[team2]['losses'] += 1
            
            # Calculate win rates
            for team, stats in team_stats.items():
                if stats['matches_played'] > 0:
                    stats['win_rate'] = round((stats['wins'] / stats['matches_played']) * 100, 1)
            
            return team_stats
            
        except Exception as e:
            return {}


# Example usage
if __name__ == "__main__":
    scraper = MatchesScraper()
    
    # Test URL
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    print("🏆 VLR Matches Scraper Test")
    print("=" * 40)
    
    try:
        def progress_callback(message):
            print(f"📊 {message}")
        
        # Scrape matches
        matches_data = scraper.scrape_matches(test_url, progress_callback)
        
        print(f"\n✅ Scraping completed!")
        print(f"   🏆 Total matches: {matches_data['total_matches']}")
        print(f"   📊 Series found: {len(matches_data['series_info'])}")
        
        # Show sample match
        if matches_data['matches']:
            sample = matches_data['matches'][0]
            print(f"   📋 Sample match: {sample.get('team1', 'N/A')} vs {sample.get('team2', 'N/A')}")
        
        # Calculate team performance
        team_perf = scraper.get_team_performance(matches_data)
        print(f"   🏅 Teams analyzed: {len(team_perf)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
