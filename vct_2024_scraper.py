import requests
from bs4 import BeautifulSoup
import time
import json
from datetime import datetime

class VCT2024Scraper:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # 2024 VCT Event URLs
        self.events = {
            'Champions Seoul 2024': {
                'url': 'https://www.vlr.gg/event/matches/2097/valorant-champions-2024',
                'event_id': '2097',
                'dates': 'Aug 1 - 25, 2024',
                'location': 'Seoul & Incheon, South Korea'
            },
            'Masters Madrid 2024': {
                'url': 'https://www.vlr.gg/event/matches/1921/champions-tour-2024-masters-madrid',
                'event_id': '1921',
                'dates': 'Mar 14 - 24, 2024',
                'location': 'Madrid, Spain'
            },
            'Masters Shanghai 2024': {
                'url': 'https://www.vlr.gg/event/matches/1999/champions-tour-2024-masters-shanghai',
                'event_id': '1999',
                'dates': 'May 23 - Jun 9, 2024',
                'location': 'Shanghai, China'
            }
        }

        self.all_matches = []

    def fetch_event_matches(self, event_name, event_data):
        """Fetch all matches from a specific VCT 2024 event"""
        print(f"\n🏆 Fetching matches from {event_name}")
        print(f"📅 {event_data['dates']}")
        print(f"📍 {event_data['location']}")
        print("-" * 60)

        try:
            response = requests.get(event_data['url'], headers=self.headers)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Find all match containers using the correct class
            match_containers = soup.find_all('a', class_='wf-module-item')

            event_matches = []

            for container in match_containers:
                # Extract match details from the container
                match_data = self.extract_match_details(container, event_name, event_data)
                if match_data:
                    event_matches.append(match_data)
                    print(f"✅ {match_data['team1']} vs {match_data['team2']} - {match_data['stage']}")

            print(f"\n📊 Found {len(event_matches)} matches for {event_name}")
            return event_matches

        except requests.RequestException as e:
            print(f"❌ Error fetching {event_name}: {e}")
            return []

    def extract_match_details(self, container, event_name, event_data):
        """Extract match details from a match container"""
        try:
            # Get match URL
            match_url = 'https://www.vlr.gg' + container.get('href', '')

            # Extract team names from the container
            team_elements = container.find_all('div', class_='match-item-vs-team-name')
            if len(team_elements) < 2:
                return None

            team1 = team_elements[0].get_text(strip=True)
            team2 = team_elements[1].get_text(strip=True)

            # Extract event/stage info
            event_elements = container.find_all('div', class_='match-item-event')
            stage = event_elements[0].get_text(strip=True) if event_elements else 'Unknown Stage'

            # Extract time info
            time_elements = container.find_all('div', class_='match-item-time')
            match_time = time_elements[0].get_text(strip=True) if time_elements else 'Time TBD'

            # Extract score if available
            score_elements = container.find_all('div', class_='match-item-vs-team-score')
            score1 = score_elements[0].get_text(strip=True) if len(score_elements) > 0 else ''
            score2 = score_elements[1].get_text(strip=True) if len(score_elements) > 1 else ''

            # Check if match is completed
            status = 'Completed' if 'Completed' in container.get_text() else 'Scheduled'

            return {
                'event': event_name,
                'event_dates': event_data['dates'],
                'location': event_data['location'],
                'team1': team1,
                'team2': team2,
                'score1': score1,
                'score2': score2,
                'stage': stage,
                'time': match_time,
                'status': status,
                'match_url': match_url,
                'scraped_at': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"⚠️  Error extracting match details: {e}")
            return None



    def scrape_all_events(self):
        """Scrape matches from all 2024 VCT events"""
        print("🎮 VCT 2024 Match Scraper")
        print("=" * 60)

        for event_name, event_data in self.events.items():
            matches = self.fetch_event_matches(event_name, event_data)
            self.all_matches.extend(matches)

            # Add delay between requests to be respectful
            time.sleep(2)

        return self.all_matches

    def save_to_json(self, filename='vct_2024_matches.json'):
        """Save scraped matches to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'scraped_at': datetime.now().isoformat(),
                    'total_matches': len(self.all_matches),
                    'events_scraped': list(self.events.keys()),
                    'matches': self.all_matches
                }, f, indent=2, ensure_ascii=False)

            print(f"\n💾 Data saved to {filename}")

        except Exception as e:
            print(f"❌ Error saving to JSON: {e}")

    def print_summary(self):
        """Print summary of scraped data"""
        print(f"\n📈 SCRAPING SUMMARY")
        print("=" * 60)
        print(f"Total matches found: {len(self.all_matches)}")

        # Group by event
        event_counts = {}
        for match in self.all_matches:
            event = match['event']
            event_counts[event] = event_counts.get(event, 0) + 1

        for event, count in event_counts.items():
            print(f"  • {event}: {count} matches")

        print("\n🎯 Recent matches:")
        for match in self.all_matches[:5]:  # Show first 5 matches
            print(f"  • {match['team1']} vs {match['team2']} ({match['event']})")

if __name__ == "__main__":
    scraper = VCT2024Scraper()

    # Scrape all events
    matches = scraper.scrape_all_events()

    # Print summary
    scraper.print_summary()

    # Save to JSON
    scraper.save_to_json()

    print(f"\n✅ Scraping completed! Found {len(matches)} total matches from VCT 2024 events.")
