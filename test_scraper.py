#!/usr/bin/env python3
"""
Test script for VLR Comprehensive Scraper
This script tests the basic functionality of the scraper
"""

from vlr_comprehensive_scraper import VLRComprehensiveScraper
import json

def test_url_construction():
    """Test URL construction functionality"""
    print("🧪 Testing URL Construction")
    print("=" * 40)
    
    scraper = VLRComprehensiveScraper()
    
    # Test URL
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    try:
        # Test event ID extraction
        event_id = scraper.extract_event_id(test_url)
        print(f"✅ Event ID extracted: {event_id}")
        
        # Test URL construction
        urls = scraper.construct_tab_urls(test_url)
        print(f"✅ URLs constructed successfully:")
        print(f"   🏆 Matches: {urls['matches']}")
        print(f"   📊 Stats: {urls['stats']}")
        print(f"   🎭 Agents: {urls['agents']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in URL construction: {e}")
        return False

def test_url_validation():
    """Test URL validation functionality"""
    print("\n🧪 Testing URL Validation")
    print("=" * 40)
    
    scraper = VLRComprehensiveScraper()
    
    test_cases = [
        ("https://www.vlr.gg/event/2097/valorant-champions-2024", True),
        ("https://www.vlr.gg/event/1921/champions-tour-2024-masters-madrid", True),
        ("https://invalid-url.com", False),
        ("", False),
        ("https://www.vlr.gg/invalid", False)
    ]
    
    all_passed = True
    
    for url, expected_valid in test_cases:
        try:
            is_valid, message = scraper.validate_url(url)
            
            if is_valid == expected_valid:
                status = "✅"
            else:
                status = "❌"
                all_passed = False
            
            print(f"{status} {url[:50]}... -> {is_valid} ({message})")
            
        except Exception as e:
            print(f"❌ Error validating {url}: {e}")
            all_passed = False
    
    return all_passed

def test_event_info_scraping():
    """Test event info scraping"""
    print("\n🧪 Testing Event Info Scraping")
    print("=" * 40)
    
    scraper = VLRComprehensiveScraper()
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    try:
        event_info = scraper.scrape_event_info(test_url)
        
        print(f"✅ Event info scraped successfully:")
        print(f"   📋 Title: {event_info.get('title', 'Not found')}")
        print(f"   📅 Dates: {event_info.get('dates', 'Not found')}")
        print(f"   📍 Location: {event_info.get('location', 'Not found')}")
        print(f"   💰 Prize Pool: {event_info.get('prize_pool', 'Not found')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error scraping event info: {e}")
        return False

def test_matches_scraping():
    """Test matches tab scraping"""
    print("\n🧪 Testing Matches Scraping")
    print("=" * 40)
    
    scraper = VLRComprehensiveScraper()
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    try:
        urls = scraper.construct_tab_urls(test_url)
        matches_data = scraper.scrape_matches_tab(urls['matches'])
        
        matches = matches_data.get('matches', [])
        print(f"✅ Matches scraped successfully:")
        print(f"   🏆 Total matches: {len(matches)}")
        
        if matches:
            sample_match = matches[0]
            print(f"   📋 Sample match: {sample_match.get('team1', 'N/A')} vs {sample_match.get('team2', 'N/A')}")
            print(f"   📊 Score: {sample_match.get('score1', 'N/A')} - {sample_match.get('score2', 'N/A')}")
            print(f"   🎯 Stage: {sample_match.get('stage', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error scraping matches: {e}")
        return False

def test_stats_scraping():
    """Test stats tab scraping"""
    print("\n🧪 Testing Player Stats Scraping")
    print("=" * 40)
    
    scraper = VLRComprehensiveScraper()
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    try:
        urls = scraper.construct_tab_urls(test_url)
        stats_data = scraper.scrape_stats_tab(urls['stats'])
        
        player_stats = stats_data.get('player_stats', [])
        print(f"✅ Player stats scraped successfully:")
        print(f"   👥 Total players: {len(player_stats)}")
        
        if player_stats:
            sample_player = player_stats[0]
            print(f"   🎯 Sample player: {sample_player.get('player', 'N/A')}")
            print(f"   🏅 Team: {sample_player.get('team', 'N/A')}")
            print(f"   📊 ACS: {sample_player.get('acs', 'N/A')}")
            print(f"   🎯 K/D: {sample_player.get('kd_ratio', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error scraping player stats: {e}")
        return False

def test_agents_scraping():
    """Test agents tab scraping"""
    print("\n🧪 Testing Agent Usage Scraping")
    print("=" * 40)
    
    scraper = VLRComprehensiveScraper()
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    try:
        urls = scraper.construct_tab_urls(test_url)
        agents_data = scraper.scrape_agents_tab(urls['agents'])
        
        agent_stats = agents_data.get('agent_stats', [])
        print(f"✅ Agent usage scraped successfully:")
        print(f"   🎭 Total agents: {len(agent_stats)}")
        
        if agent_stats:
            sample_agent = agent_stats[0]
            print(f"   🎯 Sample agent: {sample_agent.get('agent', 'N/A')}")
            print(f"   📊 Usage count: {sample_agent.get('usage_count', 'N/A')}")
            print(f"   📈 Usage percentage: {sample_agent.get('usage_percentage', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error scraping agent usage: {e}")
        return False

def run_comprehensive_test():
    """Run a comprehensive test of all functionality"""
    print("\n🧪 Running Comprehensive Test")
    print("=" * 40)
    
    scraper = VLRComprehensiveScraper()
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    try:
        def progress_callback(message):
            print(f"📊 {message}")
        
        # Run comprehensive scraping
        data = scraper.scrape_comprehensive_data(test_url, progress_callback)
        
        print(f"\n✅ Comprehensive scraping completed:")
        print(f"   📋 Event: {data['event_info'].get('title', 'Unknown')}")
        print(f"   🏆 Matches: {data['matches_data'].get('total_matches', 0)}")
        print(f"   👥 Players: {data['stats_data'].get('total_players', 0)}")
        print(f"   🎭 Agents: {data['agents_data'].get('total_agents', 0)}")
        
        # Test saving
        filename = scraper.save_to_json(data, 'test_output')
        print(f"   💾 Saved to: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in comprehensive test: {e}")
        return False

def main():
    """Main test function"""
    print("🎮 VLR Comprehensive Scraper - Test Suite")
    print("=" * 50)
    
    tests = [
        ("URL Construction", test_url_construction),
        ("URL Validation", test_url_validation),
        ("Event Info Scraping", test_event_info_scraping),
        ("Matches Scraping", test_matches_scraping),
        ("Player Stats Scraping", test_stats_scraping),
        ("Agent Usage Scraping", test_agents_scraping),
        ("Comprehensive Test", run_comprehensive_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! The scraper is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the output above.")

if __name__ == "__main__":
    main()
