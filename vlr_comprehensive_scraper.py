import requests
from bs4 import BeautifulSoup
import time
import json
import re
from datetime import datetime
from urllib.parse import urlparse
import pandas as pd

class VLRComprehensiveScraper:
    """
    Comprehensive VLR.gg scraper that extracts data from multiple tabs:
    1. Matches tab - match details and scores
    2. Stats tab - player statistics for the event
    3. Agents tab - agent usage statistics for the event
    """

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def extract_event_id(self, main_url):
        """
        Extract event ID from main VLR.gg event URL
        Example: https://www.vlr.gg/event/2097/valorant-champions-2024 -> 2097
        """
        try:
            # Use regex to extract event ID
            match = re.search(r'/event/(\d+)/', main_url)
            if match:
                return match.group(1)
            else:
                raise ValueError("Could not extract event ID from URL")
        except Exception as e:
            raise ValueError(f"Invalid URL format: {e}")

    def construct_tab_urls(self, main_url):
        """
        Construct URLs for different tabs based on main event URL
        """
        try:
            event_id = self.extract_event_id(main_url)

            # Extract event name from URL
            url_parts = main_url.split('/')
            event_name = url_parts[-1] if url_parts[-1] else url_parts[-2]

            base_url = f"https://www.vlr.gg/event"

            urls = {
                'main': main_url,
                'matches': f"{base_url}/matches/{event_id}/{event_name}",
                'stats': f"{base_url}/stats/{event_id}/{event_name}",
                'agents': f"{base_url}/agents/{event_id}/{event_name}",
                'event_id': event_id,
                'event_name': event_name
            }

            return urls

        except Exception as e:
            raise ValueError(f"Error constructing tab URLs: {e}")

    def validate_url(self, url):
        """Validate VLR.gg event URL"""
        if not url:
            return False, "Please enter a URL"

        if not re.match(r'https?://www\.vlr\.gg/event/\d+/', url):
            return False, "Invalid VLR.gg event URL format. Expected: https://www.vlr.gg/event/{id}/{name}"

        try:
            response = self.session.head(url, timeout=10)
            if response.status_code == 200:
                return True, "Valid URL"
            else:
                return False, f"URL returned status {response.status_code}"
        except requests.RequestException as e:
            return False, f"Connection error: {str(e)}"

    def scrape_event_info(self, main_url):
        """Scrape basic event information from main event page"""
        try:
            response = self.session.get(main_url, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            event_info = {
                'url': main_url,
                'scraped_at': datetime.now().isoformat()
            }

            # Event title
            title_elem = soup.find('h1', class_='wf-title')
            if title_elem:
                event_info['title'] = title_elem.get_text(strip=True)

            # Event description and details
            desc_elem = soup.find('div', class_='event-desc')
            if desc_elem:
                desc_text = desc_elem.get_text(strip=True)
                event_info['description'] = desc_text

                # Extract dates and location from description
                lines = desc_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if any(month in line for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']):
                        event_info['dates'] = line
                    elif any(word in line.lower() for word in ['korea', 'spain', 'china', 'usa', 'brazil',
                                                              'germany', 'france', 'japan', 'turkey']):
                        event_info['location'] = line

            # Prize pool
            prize_elem = soup.find('div', class_='prize-pool')
            if prize_elem:
                event_info['prize_pool'] = prize_elem.get_text(strip=True)

            # Event status
            status_elem = soup.find('div', class_='event-status')
            if status_elem:
                event_info['status'] = status_elem.get_text(strip=True)

            return event_info

        except Exception as e:
            raise Exception(f"Error scraping event info: {e}")

    def scrape_matches_tab(self, matches_url, progress_callback=None):
        """
        Scrape matches data from the matches tab
        Includes match details, scores, and series information
        """
        try:
            if progress_callback:
                progress_callback("Fetching matches data...")

            response = self.session.get(matches_url, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')
            matches = []

            # Find all match containers
            match_containers = soup.find_all('a', class_='wf-module-item')

            for i, container in enumerate(match_containers):
                if progress_callback and i % 10 == 0:
                    progress_callback(f"Processing match {i+1}/{len(match_containers)}")

                match_data = self._extract_match_details(container)
                if match_data:
                    matches.append(match_data)

                # Small delay to be respectful
                time.sleep(0.1)

            # Also look for series information if available
            series_data = self._extract_series_info(soup)

            return {
                'matches': matches,
                'series_info': series_data,
                'total_matches': len(matches),
                'scraped_from': matches_url,
                'scraped_at': datetime.now().isoformat()
            }

        except Exception as e:
            raise Exception(f"Error scraping matches tab: {e}")

    def _extract_match_details(self, container):
        """Extract detailed match information from match container"""
        try:
            # Get match URL
            match_url = 'https://www.vlr.gg' + container.get('href', '')

            # Extract team names
            team_elements = container.find_all('div', class_='match-item-vs-team-name')
            if len(team_elements) < 2:
                return None

            team1 = team_elements[0].get_text(strip=True)
            team2 = team_elements[1].get_text(strip=True)

            # Extract scores
            score_elements = container.find_all('div', class_='match-item-vs-team-score')
            score1 = score_elements[0].get_text(strip=True) if len(score_elements) > 0 else ''
            score2 = score_elements[1].get_text(strip=True) if len(score_elements) > 1 else ''

            # Extract stage/round information
            event_elements = container.find_all('div', class_='match-item-event')
            stage = event_elements[0].get_text(strip=True) if event_elements else 'Unknown Stage'

            # Extract time information
            time_elements = container.find_all('div', class_='match-item-time')
            match_time = time_elements[0].get_text(strip=True) if time_elements else 'Time TBD'

            # Extract series ID if available
            series_id = None
            href = container.get('href', '')
            series_match = re.search(r'series_id=(\d+)', href)
            if series_match:
                series_id = series_match.group(1)

            # Determine match status
            status = 'Completed' if score1 and score2 and score1.isdigit() and score2.isdigit() else 'Scheduled'

            # Extract additional match metadata
            match_data = {
                'team1': team1,
                'team2': team2,
                'score1': score1,
                'score2': score2,
                'stage': stage,
                'time': match_time,
                'status': status,
                'match_url': match_url,
                'series_id': series_id,
                'scraped_at': datetime.now().isoformat()
            }

            # Try to extract winner if match is completed
            if status == 'Completed' and score1.isdigit() and score2.isdigit():
                s1, s2 = int(score1), int(score2)
                if s1 > s2:
                    match_data['winner'] = team1
                elif s2 > s1:
                    match_data['winner'] = team2
                else:
                    match_data['winner'] = 'Draw'

            return match_data

        except Exception as e:
            return None

    def _extract_series_info(self, soup):
        """Extract series information from matches page"""
        try:
            series_info = []

            # Look for series containers or brackets
            series_containers = soup.find_all('div', class_='bracket-series')

            for container in series_containers:
                series_data = {}

                # Extract series title
                title_elem = container.find('div', class_='series-title')
                if title_elem:
                    series_data['title'] = title_elem.get_text(strip=True)

                # Extract series matches
                series_matches = container.find_all('a', class_='wf-module-item')
                series_data['matches_count'] = len(series_matches)

                if series_data:
                    series_info.append(series_data)

            return series_info

        except Exception:
            return []

    def scrape_stats_tab(self, stats_url, progress_callback=None):
        """
        Scrape player statistics from the stats tab
        This captures overall event statistics for all players
        """
        try:
            if progress_callback:
                progress_callback("Fetching player statistics...")

            response = self.session.get(stats_url, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Find the main stats table
            stats_table = soup.find('table', class_='wf-table-inset')
            if not stats_table:
                # Try alternative selectors
                stats_table = soup.find('table', class_='stats-table')
                if not stats_table:
                    stats_table = soup.find('table')

            if not stats_table:
                raise Exception("Could not find stats table on the page")

            player_stats = []
            rows = stats_table.find_all('tr')

            # Skip header row
            for i, row in enumerate(rows[1:], 1):
                if progress_callback and i % 20 == 0:
                    progress_callback(f"Processing player {i}/{len(rows)-1}")

                player_data = self._extract_player_stats(row)
                if player_data:
                    player_stats.append(player_data)

            return {
                'player_stats': player_stats,
                'total_players': len(player_stats),
                'scraped_from': stats_url,
                'scraped_at': datetime.now().isoformat()
            }

        except Exception as e:
            raise Exception(f"Error scraping stats tab: {e}")

    def _extract_player_stats(self, row):
        """Extract player statistics from a table row"""
        try:
            cells = row.find_all('td')
            if len(cells) < 8:  # Minimum expected columns
                return None

            # Extract player name and team
            player_cell = cells[0]
            player_name = None
            team_name = None

            # Try different selectors for player name
            player_elem = player_cell.find('div', class_='text-of')
            if not player_elem:
                player_elem = player_cell.find('a', class_='text-of')
            if not player_elem:
                player_elem = player_cell.find('span', class_='text-of')

            if player_elem:
                player_name = player_elem.get_text(strip=True)

            # Extract team from image or text
            team_elem = row.find('img', class_='team-logo')
            if team_elem:
                team_name = team_elem.get('alt', 'Unknown')
            else:
                # Try to find team in text
                team_elem = player_cell.find('div', class_='team-name')
                if team_elem:
                    team_name = team_elem.get_text(strip=True)

            if not player_name:
                return None

            # Extract statistics (adjust indices based on actual table structure)
            stats = {
                'player': player_name,
                'team': team_name or 'Unknown',
                'rating': self._safe_extract_text(cells, 1),
                'acs': self._safe_extract_text(cells, 2),
                'kills': self._safe_extract_text(cells, 3),
                'deaths': self._safe_extract_text(cells, 4),
                'assists': self._safe_extract_text(cells, 5),
                'plus_minus': self._safe_extract_text(cells, 6),
                'adr': self._safe_extract_text(cells, 7),
                'hs_percent': self._safe_extract_text(cells, 8),
                'first_kills': self._safe_extract_text(cells, 9),
                'first_deaths': self._safe_extract_text(cells, 10),
                'scraped_at': datetime.now().isoformat()
            }

            # Calculate K/D ratio
            try:
                k = float(stats['kills']) if stats['kills'].replace('.', '').isdigit() else 0
                d = float(stats['deaths']) if stats['deaths'].replace('.', '').isdigit() else 1
                stats['kd_ratio'] = f"{k/d:.2f}" if d > 0 else "∞"
            except:
                stats['kd_ratio'] = "0.00"

            return stats

        except Exception as e:
            return None

    def _safe_extract_text(self, cells, index):
        """Safely extract text from table cell at given index"""
        try:
            if index < len(cells):
                return cells[index].get_text(strip=True)
            return '0'
        except:
            return '0'

    def scrape_agents_tab(self, agents_url, progress_callback=None):
        """
        Scrape agent usage statistics from the agents tab
        This captures the first table showing agent usage percentages
        """
        try:
            if progress_callback:
                progress_callback("Fetching agent usage data...")

            response = self.session.get(agents_url, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Find the first table (agent usage statistics)
            agents_table = soup.find('table', class_='wf-table-inset')
            if not agents_table:
                # Try alternative selectors
                agents_table = soup.find('table', class_='agents-table')
                if not agents_table:
                    agents_table = soup.find('table')

            if not agents_table:
                raise Exception("Could not find agents table on the page")

            agent_stats = []
            rows = agents_table.find_all('tr')

            # Skip header row
            for i, row in enumerate(rows[1:], 1):
                if progress_callback and i % 10 == 0:
                    progress_callback(f"Processing agent {i}/{len(rows)-1}")

                agent_data = self._extract_agent_stats(row)
                if agent_data:
                    agent_stats.append(agent_data)

            return {
                'agent_stats': agent_stats,
                'total_agents': len(agent_stats),
                'scraped_from': agents_url,
                'scraped_at': datetime.now().isoformat()
            }

        except Exception as e:
            raise Exception(f"Error scraping agents tab: {e}")

    def _extract_agent_stats(self, row):
        """Extract agent statistics from a table row"""
        try:
            cells = row.find_all('td')
            if len(cells) < 3:  # Minimum expected columns
                return None

            # Extract agent name
            agent_cell = cells[0]
            agent_name = None

            # Try to find agent image first
            agent_img = agent_cell.find('img')
            if agent_img:
                # Get agent name from alt text or src
                agent_name = agent_img.get('alt', '')
                if not agent_name and agent_img.get('src'):
                    src = agent_img.get('src')
                    if '/agents/' in src:
                        agent_name = src.split('/agents/')[-1].split('.')[0].title()

            # If no image, try text
            if not agent_name:
                agent_name = agent_cell.get_text(strip=True)

            if not agent_name:
                return None

            # Extract usage statistics
            stats = {
                'agent': agent_name,
                'usage_count': self._safe_extract_text(cells, 1),
                'usage_percentage': self._safe_extract_text(cells, 2),
                'win_rate': self._safe_extract_text(cells, 3) if len(cells) > 3 else 'N/A',
                'avg_rating': self._safe_extract_text(cells, 4) if len(cells) > 4 else 'N/A',
                'avg_acs': self._safe_extract_text(cells, 5) if len(cells) > 5 else 'N/A',
                'scraped_at': datetime.now().isoformat()
            }

            return stats

        except Exception as e:
            return None

    def scrape_comprehensive_data(self, main_url, progress_callback=None):
        """
        Main method to scrape all data from all tabs
        """
        try:
            # Construct URLs for all tabs
            urls = self.construct_tab_urls(main_url)

            if progress_callback:
                progress_callback("Constructing tab URLs...")

            # Initialize result structure
            result = {
                'event_info': {},
                'matches_data': {},
                'stats_data': {},
                'agents_data': {},
                'urls': urls,
                'scraped_at': datetime.now().isoformat()
            }

            # 1. Scrape event info from main page
            if progress_callback:
                progress_callback("Scraping event information...")
            result['event_info'] = self.scrape_event_info(main_url)

            # 2. Scrape matches data
            if progress_callback:
                progress_callback("Scraping matches data...")
            result['matches_data'] = self.scrape_matches_tab(urls['matches'], progress_callback)

            # 3. Scrape player stats
            if progress_callback:
                progress_callback("Scraping player statistics...")
            result['stats_data'] = self.scrape_stats_tab(urls['stats'], progress_callback)

            # 4. Scrape agent data
            if progress_callback:
                progress_callback("Scraping agent usage data...")
            result['agents_data'] = self.scrape_agents_tab(urls['agents'], progress_callback)

            if progress_callback:
                progress_callback("Scraping completed successfully!")

            return result

        except Exception as e:
            raise Exception(f"Error in comprehensive scraping: {e}")

    def save_to_json(self, data, filename_prefix='vlr_comprehensive_data'):
        """Save scraped data to JSON file"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{filename_prefix}_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            return filename

        except Exception as e:
            raise Exception(f"Error saving to JSON: {e}")

    def save_separate_files(self, data, base_filename='vlr_data'):
        """Save data into separate JSON files for each category"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            files_created = []

            # Save matches data
            if data.get('matches_data'):
                matches_filename = f"matches_{base_filename}_{timestamp}.json"
                matches_data = {
                    'event_info': data.get('event_info', {}),
                    'matches_data': data['matches_data'],
                    'scraped_at': timestamp
                }

                with open(matches_filename, 'w', encoding='utf-8') as f:
                    json.dump(matches_data, f, indent=2, ensure_ascii=False)
                files_created.append(matches_filename)

            # Save player stats data
            if data.get('stats_data'):
                stats_filename = f"player_stats_{base_filename}_{timestamp}.json"
                stats_data = {
                    'event_info': data.get('event_info', {}),
                    'stats_data': data['stats_data'],
                    'scraped_at': timestamp
                }

                with open(stats_filename, 'w', encoding='utf-8') as f:
                    json.dump(stats_data, f, indent=2, ensure_ascii=False)
                files_created.append(stats_filename)

            # Save agents data
            if data.get('agents_data'):
                agents_filename = f"agents_{base_filename}_{timestamp}.json"
                agents_data = {
                    'event_info': data.get('event_info', {}),
                    'agents_data': data['agents_data'],
                    'scraped_at': timestamp
                }

                with open(agents_filename, 'w', encoding='utf-8') as f:
                    json.dump(agents_data, f, indent=2, ensure_ascii=False)
                files_created.append(agents_filename)

            return files_created

        except Exception as e:
            raise Exception(f"Error saving separate files: {e}")


# Example usage
if __name__ == "__main__":
    scraper = VLRComprehensiveScraper()

    # Example URL
    main_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"

    try:
        print("🎮 VLR Comprehensive Scraper")
        print("=" * 50)

        # Validate URL
        is_valid, message = scraper.validate_url(main_url)
        if not is_valid:
            print(f"❌ {message}")
            exit(1)

        print(f"✅ {message}")

        # Scrape all data
        def progress_print(message):
            print(f"📊 {message}")

        data = scraper.scrape_comprehensive_data(main_url, progress_print)

        # Print summary
        print("\n📈 SCRAPING SUMMARY")
        print("=" * 50)
        print(f"Event: {data['event_info'].get('title', 'Unknown')}")
        print(f"Matches found: {data['matches_data'].get('total_matches', 0)}")
        print(f"Players analyzed: {data['stats_data'].get('total_players', 0)}")
        print(f"Agents tracked: {data['agents_data'].get('total_agents', 0)}")

        # Save data
        filename = scraper.save_to_json(data)
        print(f"\n💾 Data saved to: {filename}")

        # Save separate files
        separate_files = scraper.save_separate_files(data)
        print(f"📁 Separate files created: {', '.join(separate_files)}")

        print("\n✅ Scraping completed successfully!")

    except Exception as e:
        print(f"❌ Error: {e}")
