#!/usr/bin/env python3
"""
Test script for database integration with VLR scraper
This script tests the complete workflow: scrape -> save to database -> retrieve from database
"""

from vlr_comprehensive_scraper import VLRComprehensiveScraper
from vlr_database import VLRDatabase
import j<PERSON>

def test_complete_workflow():
    """Test the complete scraping and database workflow"""
    print("🎮 VLR Scraper Database Integration Test")
    print("=" * 50)
    
    # Initialize components
    scraper = VLRComprehensiveScraper()
    db = VLRDatabase("test_integration.db")
    
    # Test URL
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    print(f"🔗 Testing with URL: {test_url}")
    
    # Step 1: Validate URL
    print("\n📋 Step 1: Validating URL...")
    is_valid, message = scraper.validate_url(test_url)
    if not is_valid:
        print(f"❌ URL validation failed: {message}")
        return False
    print(f"✅ URL is valid: {message}")
    
    # Step 2: Scrape data (limited for testing)
    print("\n📋 Step 2: Scraping data...")
    try:
        # Get URLs
        urls = scraper.construct_tab_urls(test_url)
        print(f"   🏆 Matches URL: {urls['matches']}")
        print(f"   📊 Stats URL: {urls['stats']}")
        print(f"   🎭 Agents URL: {urls['agents']}")
        
        # Scrape event info only for quick test
        event_info = scraper.scrape_event_info(test_url)
        print(f"   📋 Event: {event_info.get('title', 'Unknown')}")
        
        # Create minimal test data structure
        test_data = {
            'event_info': event_info,
            'matches_data': {
                'matches': [
                    {
                        'team1': 'Test Team A',
                        'team2': 'Test Team B',
                        'score1': '2',
                        'score2': '1',
                        'stage': 'Test Stage',
                        'status': 'Completed',
                        'winner': 'Test Team A',
                        'time': 'Test Time',
                        'match_url': 'https://test.url',
                        'scraped_at': '2024-05-30T13:00:00'
                    }
                ],
                'total_matches': 1,
                'scraped_at': '2024-05-30T13:00:00'
            },
            'stats_data': {
                'player_stats': [
                    {
                        'player': 'TestPlayer1',
                        'team': 'Test Team A',
                        'rating': '1.25',
                        'acs': '250',
                        'kills': '20',
                        'deaths': '15',
                        'assists': '8',
                        'kd_ratio': '1.33',
                        'adr': '160',
                        'hs_percent': '25%',
                        'scraped_at': '2024-05-30T13:00:00'
                    }
                ],
                'total_players': 1,
                'scraped_at': '2024-05-30T13:00:00'
            },
            'agents_data': {
                'agent_stats': [
                    {
                        'agent': 'Jett',
                        'usage_count': '10',
                        'usage_percentage': '25%',
                        'win_rate': '60%',
                        'avg_rating': '1.20',
                        'avg_acs': '240',
                        'scraped_at': '2024-05-30T13:00:00'
                    }
                ],
                'total_agents': 1,
                'scraped_at': '2024-05-30T13:00:00'
            },
            'urls': urls,
            'scraped_at': '2024-05-30T13:00:00'
        }
        
        print("✅ Test data created successfully")
        
    except Exception as e:
        print(f"❌ Error during scraping: {e}")
        return False
    
    # Step 3: Save to database
    print("\n📋 Step 3: Saving to database...")
    try:
        event_id = db.save_comprehensive_data(test_data)
        print(f"✅ Data saved to database with event ID: {event_id}")
    except Exception as e:
        print(f"❌ Error saving to database: {e}")
        return False
    
    # Step 4: Retrieve from database
    print("\n📋 Step 4: Retrieving from database...")
    try:
        # Get database stats
        db_stats = db.get_database_stats()
        print(f"   📊 Database stats: {db_stats}")
        
        # Get events list
        events_df = db.get_events_list()
        print(f"   📋 Events in database: {len(events_df)}")
        
        # Get specific event data
        event_data = db.get_event_data(event_id)
        print(f"   🏆 Matches retrieved: {len(event_data['matches'])}")
        print(f"   👥 Players retrieved: {len(event_data['player_stats'])}")
        print(f"   🎭 Agents retrieved: {len(event_data['agent_usage'])}")
        
        print("✅ Data retrieved successfully from database")
        
    except Exception as e:
        print(f"❌ Error retrieving from database: {e}")
        return False
    
    # Step 5: Test database operations
    print("\n📋 Step 5: Testing database operations...")
    try:
        # Test database size
        db_size = db.get_database_size()
        print(f"   💾 Database size: {db_size}")
        
        # Test export (optional)
        # export_files = db.export_to_csv(event_id, "test_exports")
        # print(f"   📁 Export files created: {len(export_files)}")
        
        print("✅ Database operations completed successfully")
        
    except Exception as e:
        print(f"❌ Error in database operations: {e}")
        return False
    
    print("\n🎉 All tests passed! Database integration is working correctly.")
    return True

def test_database_only():
    """Test database functionality without scraping"""
    print("\n🗄️ Testing Database-Only Functionality")
    print("=" * 40)
    
    db = VLRDatabase("test_db_only.db")
    
    # Test database initialization
    print("✅ Database initialized")
    
    # Test stats
    stats = db.get_database_stats()
    print(f"📊 Initial stats: {stats}")
    
    # Test with empty database
    events_df = db.get_events_list()
    print(f"📋 Events in empty database: {len(events_df)}")
    
    print("✅ Database-only tests completed")

def main():
    """Main test function"""
    print("🧪 VLR Database Integration Test Suite")
    print("=" * 60)
    
    # Test 1: Database-only functionality
    test_database_only()
    
    # Test 2: Complete workflow (scraping + database)
    print("\n" + "=" * 60)
    success = test_complete_workflow()
    
    print("\n📊 TEST SUMMARY")
    print("=" * 60)
    if success:
        print("🎉 All integration tests passed!")
        print("✅ The scraper and database are working together correctly.")
        print("✅ You can now use the Streamlit UI with confidence.")
    else:
        print("❌ Some tests failed. Please check the output above.")
    
    print("\n🚀 To run the Streamlit UI:")
    print("   streamlit run vlr_streamlit_ui.py")

if __name__ == "__main__":
    main()
