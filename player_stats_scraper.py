import requests
from bs4 import BeautifulSoup
import time
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

class PlayerStatsScraper:
    """
    Dedicated scraper for VLR.gg player statistics
    Handles individual player performance metrics, team affiliations, and rankings
    """
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def construct_stats_url(self, main_url: str) -> str:
        """
        Construct stats URL from main event URL
        Example: https://www.vlr.gg/event/2097/valorant-champions-2024
        -> https://www.vlr.gg/event/stats/2097/valorant-champions-2024
        """
        try:
            # Extract event ID
            match = re.search(r'/event/(\d+)/', main_url)
            if not match:
                raise ValueError("Could not extract event ID from URL")
            
            event_id = match.group(1)
            
            # Extract event name from URL
            url_parts = main_url.split('/')
            event_name = url_parts[-1] if url_parts[-1] else url_parts[-2]
            
            stats_url = f"https://www.vlr.gg/event/stats/{event_id}/{event_name}"
            return stats_url
            
        except Exception as e:
            raise ValueError(f"Error constructing stats URL: {e}")
    
    def scrape_player_stats(self, main_url: str, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Scrape all player statistics from the stats tab
        """
        try:
            stats_url = self.construct_stats_url(main_url)
            
            if progress_callback:
                progress_callback("Fetching player stats page...")
            
            response = self.session.get(stats_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            if progress_callback:
                progress_callback("Parsing player statistics...")
            
            # Extract player stats from main table
            player_stats = self._extract_player_stats_table(soup, progress_callback)
            
            # Extract additional metrics if available
            additional_stats = self._extract_additional_stats(soup)
            
            # Calculate team statistics
            team_stats = self._calculate_team_stats(player_stats)
            
            result = {
                'player_stats': player_stats,
                'additional_stats': additional_stats,
                'team_stats': team_stats,
                'total_players': len(player_stats),
                'scraped_from': stats_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            if progress_callback:
                progress_callback(f"Completed! Found {len(player_stats)} players")
            
            return result
            
        except Exception as e:
            raise Exception(f"Error scraping player stats: {e}")
    
    def _extract_player_stats_table(self, soup: BeautifulSoup, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """Extract player statistics from the main stats table"""
        player_stats = []
        
        # Find the main stats table
        stats_table = soup.find('table', class_='wf-table-inset')
        if not stats_table:
            # Try alternative selectors
            stats_table = soup.find('table', class_='stats-table')
            if not stats_table:
                stats_table = soup.find('table')
        
        if not stats_table:
            return []
        
        # Get table rows
        rows = stats_table.find_all('tr')
        
        # Skip header row and process data rows
        for i, row in enumerate(rows[1:], 1):
            if progress_callback and i % 20 == 0:
                progress_callback(f"Processing player {i}/{len(rows)-1}")
            
            player_data = self._extract_player_row(row)
            if player_data:
                player_stats.append(player_data)
        
        return player_stats
    
    def _extract_player_row(self, row) -> Optional[Dict[str, Any]]:
        """Extract player statistics from a table row"""
        try:
            cells = row.find_all('td')
            if len(cells) < 5:  # Minimum expected columns
                return None
            
            player_data = {
                'scraped_at': datetime.now().isoformat()
            }
            
            # Extract player name and team (usually in first cell)
            player_cell = cells[0]
            
            # Try to find player name
            player_elem = player_cell.find(['div', 'span', 'a'], class_=re.compile(r'player|name'))
            if not player_elem:
                player_elem = player_cell.find('a')
            if not player_elem:
                player_text = player_cell.get_text(strip=True)
                if player_text:
                    player_data['player'] = player_text
            else:
                player_data['player'] = player_elem.get_text(strip=True)
            
            # Try to find team information
            team_elem = player_cell.find(['img', 'div', 'span'], class_=re.compile(r'team'))
            if team_elem:
                if team_elem.name == 'img':
                    player_data['team'] = team_elem.get('alt', 'Unknown')
                else:
                    player_data['team'] = team_elem.get_text(strip=True)
            else:
                # Try to extract team from player cell text or other cells
                team_link = row.find('a', href=re.compile(r'/team/'))
                if team_link:
                    player_data['team'] = team_link.get_text(strip=True)
                else:
                    player_data['team'] = 'Unknown'
            
            # Extract statistics from remaining cells
            # Common VLR.gg stats table structure: Player, Rating, ACS, K, D, A, +/-, ADR, HS%, FK, FD
            stats_mapping = [
                ('rating', 1),
                ('acs', 2),
                ('kills', 3),
                ('deaths', 4),
                ('assists', 5),
                ('plus_minus', 6),
                ('adr', 7),
                ('hs_percent', 8),
                ('first_kills', 9),
                ('first_deaths', 10)
            ]
            
            for stat_name, cell_index in stats_mapping:
                if cell_index < len(cells):
                    value = self._safe_extract_text(cells[cell_index])
                    player_data[stat_name] = value
            
            # Calculate K/D ratio
            try:
                kills = float(player_data.get('kills', '0'))
                deaths = float(player_data.get('deaths', '1'))
                if deaths > 0:
                    player_data['kd_ratio'] = round(kills / deaths, 2)
                else:
                    player_data['kd_ratio'] = float('inf') if kills > 0 else 0
            except (ValueError, TypeError):
                player_data['kd_ratio'] = 0
            
            # Calculate KAST if assists are available
            try:
                kills = float(player_data.get('kills', '0'))
                assists = float(player_data.get('assists', '0'))
                # This is a simplified KAST calculation
                player_data['ka_sum'] = kills + assists
            except (ValueError, TypeError):
                player_data['ka_sum'] = 0
            
            # Only return if we have essential data
            if player_data.get('player'):
                return player_data
            
            return None
            
        except Exception as e:
            return None
    
    def _safe_extract_text(self, cell) -> str:
        """Safely extract text from table cell"""
        try:
            text = cell.get_text(strip=True)
            # Clean up common formatting
            text = text.replace('%', '').replace('+', '').replace('-', '')
            return text if text else '0'
        except:
            return '0'
    
    def _extract_additional_stats(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract additional statistics and metrics"""
        try:
            additional_stats = {}
            
            # Look for additional stat tables or sections
            stat_sections = soup.find_all(['div', 'section'], class_=re.compile(r'stats|metrics'))
            
            for section in stat_sections:
                # Extract section title
                title_elem = section.find(['h1', 'h2', 'h3', 'h4'])
                if title_elem:
                    section_title = title_elem.get_text(strip=True)
                    
                    # Extract stats from this section
                    section_stats = []
                    stat_rows = section.find_all('tr')
                    
                    for row in stat_rows[1:]:  # Skip header
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            stat_name = cells[0].get_text(strip=True)
                            stat_value = cells[1].get_text(strip=True)
                            section_stats.append({
                                'name': stat_name,
                                'value': stat_value
                            })
                    
                    if section_stats:
                        additional_stats[section_title] = section_stats
            
            return additional_stats
            
        except Exception:
            return {}
    
    def _calculate_team_stats(self, player_stats: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Calculate team-level statistics from player data"""
        try:
            team_stats = {}
            
            for player in player_stats:
                team = player.get('team', 'Unknown')
                if team == 'Unknown':
                    continue
                
                if team not in team_stats:
                    team_stats[team] = {
                        'player_count': 0,
                        'total_acs': 0,
                        'total_kills': 0,
                        'total_deaths': 0,
                        'total_assists': 0,
                        'avg_acs': 0,
                        'avg_kd': 0,
                        'avg_rating': 0
                    }
                
                # Accumulate stats
                team_stats[team]['player_count'] += 1
                
                try:
                    team_stats[team]['total_acs'] += float(player.get('acs', '0'))
                    team_stats[team]['total_kills'] += float(player.get('kills', '0'))
                    team_stats[team]['total_deaths'] += float(player.get('deaths', '0'))
                    team_stats[team]['total_assists'] += float(player.get('assists', '0'))
                except (ValueError, TypeError):
                    pass
            
            # Calculate averages
            for team, stats in team_stats.items():
                if stats['player_count'] > 0:
                    stats['avg_acs'] = round(stats['total_acs'] / stats['player_count'], 1)
                    
                    if stats['total_deaths'] > 0:
                        stats['avg_kd'] = round(stats['total_kills'] / stats['total_deaths'], 2)
                    else:
                        stats['avg_kd'] = float('inf') if stats['total_kills'] > 0 else 0
            
            return team_stats
            
        except Exception:
            return {}
    
    def get_top_performers(self, player_stats: List[Dict[str, Any]], metric: str = 'acs', top_n: int = 10) -> List[Dict[str, Any]]:
        """Get top performers by specified metric"""
        try:
            # Filter and sort players by metric
            valid_players = []
            
            for player in player_stats:
                try:
                    value = float(player.get(metric, '0'))
                    player_copy = player.copy()
                    player_copy[f'{metric}_numeric'] = value
                    valid_players.append(player_copy)
                except (ValueError, TypeError):
                    continue
            
            # Sort by metric (descending)
            sorted_players = sorted(valid_players, key=lambda x: x[f'{metric}_numeric'], reverse=True)
            
            return sorted_players[:top_n]
            
        except Exception:
            return []
    
    def get_player_rankings(self, player_stats: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Get player rankings across different metrics"""
        try:
            rankings = {}
            
            metrics = ['acs', 'kills', 'kd_ratio', 'rating', 'adr']
            
            for metric in metrics:
                rankings[metric] = self.get_top_performers(player_stats, metric, 15)
            
            return rankings
            
        except Exception:
            return {}


# Example usage
if __name__ == "__main__":
    scraper = PlayerStatsScraper()
    
    # Test URL
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    print("📊 VLR Player Stats Scraper Test")
    print("=" * 40)
    
    try:
        def progress_callback(message):
            print(f"📊 {message}")
        
        # Scrape player stats
        stats_data = scraper.scrape_player_stats(test_url, progress_callback)
        
        print(f"\n✅ Scraping completed!")
        print(f"   👥 Total players: {stats_data['total_players']}")
        print(f"   🏅 Teams found: {len(stats_data['team_stats'])}")
        
        # Show sample player
        if stats_data['player_stats']:
            sample = stats_data['player_stats'][0]
            print(f"   🎯 Sample player: {sample.get('player', 'N/A')} ({sample.get('team', 'N/A')})")
            print(f"   📈 ACS: {sample.get('acs', 'N/A')}, K/D: {sample.get('kd_ratio', 'N/A')}")
        
        # Show top performer
        top_acs = scraper.get_top_performers(stats_data['player_stats'], 'acs', 1)
        if top_acs:
            top_player = top_acs[0]
            print(f"   🏆 Top ACS: {top_player.get('player', 'N/A')} - {top_player.get('acs', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
