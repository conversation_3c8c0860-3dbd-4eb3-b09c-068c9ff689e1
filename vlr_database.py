import sqlite3
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
import os

class VLRDatabase:
    """
    SQLite database manager for VLR.gg scraped data
    Handles storage and retrieval of event data, matches, player stats, and agent usage
    """
    
    def __init__(self, db_path: str = "vlr_data.db"):
        """Initialize database connection and create tables if they don't exist"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Create database tables if they don't exist"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_id TEXT UNIQUE NOT NULL,
                    title TEXT,
                    dates TEXT,
                    location TEXT,
                    prize_pool TEXT,
                    description TEXT,
                    url TEXT,
                    scraped_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Matches table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS matches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_id TEXT,
                    team1 TEXT,
                    team2 TEXT,
                    score1 TEXT,
                    score2 TEXT,
                    stage TEXT,
                    match_time TEXT,
                    status TEXT,
                    winner TEXT,
                    match_url TEXT,
                    series_id TEXT,
                    scraped_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (event_id) REFERENCES events (event_id)
                )
            ''')
            
            # Player stats table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS player_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_id TEXT,
                    player TEXT,
                    team TEXT,
                    rating REAL,
                    acs REAL,
                    kills INTEGER,
                    deaths INTEGER,
                    assists INTEGER,
                    plus_minus INTEGER,
                    adr REAL,
                    hs_percent TEXT,
                    first_kills INTEGER,
                    first_deaths INTEGER,
                    kd_ratio REAL,
                    scraped_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (event_id) REFERENCES events (event_id)
                )
            ''')
            
            # Agent usage table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS agent_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_id TEXT,
                    agent TEXT,
                    usage_count INTEGER,
                    usage_percentage TEXT,
                    win_rate TEXT,
                    avg_rating REAL,
                    avg_acs REAL,
                    scraped_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (event_id) REFERENCES events (event_id)
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_matches_event_id ON matches (event_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_player_stats_event_id ON player_stats (event_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_agent_usage_event_id ON agent_usage (event_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_player_stats_player ON player_stats (player)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_agent_usage_agent ON agent_usage (agent)')
            
            conn.commit()
    
    def save_comprehensive_data(self, data: Dict[str, Any]) -> str:
        """
        Save comprehensive scraped data to database
        Returns the event_id for reference
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Extract event info
                event_info = data.get('event_info', {})
                urls = data.get('urls', {})
                event_id = urls.get('event_id', 'unknown')
                
                # Save event info
                cursor.execute('''
                    INSERT OR REPLACE INTO events 
                    (event_id, title, dates, location, prize_pool, description, url, scraped_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    event_id,
                    event_info.get('title'),
                    event_info.get('dates'),
                    event_info.get('location'),
                    event_info.get('prize_pool'),
                    event_info.get('description'),
                    event_info.get('url'),
                    event_info.get('scraped_at')
                ))
                
                # Save matches data
                matches_data = data.get('matches_data', {})
                matches = matches_data.get('matches', [])
                
                # Clear existing matches for this event
                cursor.execute('DELETE FROM matches WHERE event_id = ?', (event_id,))
                
                for match in matches:
                    cursor.execute('''
                        INSERT INTO matches 
                        (event_id, team1, team2, score1, score2, stage, match_time, status, 
                         winner, match_url, series_id, scraped_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        event_id,
                        match.get('team1'),
                        match.get('team2'),
                        match.get('score1'),
                        match.get('score2'),
                        match.get('stage'),
                        match.get('time'),
                        match.get('status'),
                        match.get('winner'),
                        match.get('match_url'),
                        match.get('series_id'),
                        match.get('scraped_at')
                    ))
                
                # Save player stats
                stats_data = data.get('stats_data', {})
                player_stats = stats_data.get('player_stats', [])
                
                # Clear existing player stats for this event
                cursor.execute('DELETE FROM player_stats WHERE event_id = ?', (event_id,))
                
                for player in player_stats:
                    # Convert string numbers to appropriate types
                    rating = self._safe_float(player.get('rating'))
                    acs = self._safe_float(player.get('acs'))
                    kills = self._safe_int(player.get('kills'))
                    deaths = self._safe_int(player.get('deaths'))
                    assists = self._safe_int(player.get('assists'))
                    plus_minus = self._safe_int(player.get('plus_minus'))
                    adr = self._safe_float(player.get('adr'))
                    first_kills = self._safe_int(player.get('first_kills'))
                    first_deaths = self._safe_int(player.get('first_deaths'))
                    kd_ratio = self._safe_float(player.get('kd_ratio'))
                    
                    cursor.execute('''
                        INSERT INTO player_stats 
                        (event_id, player, team, rating, acs, kills, deaths, assists, 
                         plus_minus, adr, hs_percent, first_kills, first_deaths, kd_ratio, scraped_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        event_id,
                        player.get('player'),
                        player.get('team'),
                        rating,
                        acs,
                        kills,
                        deaths,
                        assists,
                        plus_minus,
                        adr,
                        player.get('hs_percent'),
                        first_kills,
                        first_deaths,
                        kd_ratio,
                        player.get('scraped_at')
                    ))
                
                # Save agent usage data
                agents_data = data.get('agents_data', {})
                agent_stats = agents_data.get('agent_stats', [])
                
                # Clear existing agent usage for this event
                cursor.execute('DELETE FROM agent_usage WHERE event_id = ?', (event_id,))
                
                for agent in agent_stats:
                    usage_count = self._safe_int(agent.get('usage_count'))
                    avg_rating = self._safe_float(agent.get('avg_rating'))
                    avg_acs = self._safe_float(agent.get('avg_acs'))
                    
                    cursor.execute('''
                        INSERT INTO agent_usage 
                        (event_id, agent, usage_count, usage_percentage, win_rate, 
                         avg_rating, avg_acs, scraped_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        event_id,
                        agent.get('agent'),
                        usage_count,
                        agent.get('usage_percentage'),
                        agent.get('win_rate'),
                        avg_rating,
                        avg_acs,
                        agent.get('scraped_at')
                    ))
                
                conn.commit()
                return event_id
                
        except Exception as e:
            raise Exception(f"Error saving data to database: {e}")
    
    def _safe_int(self, value: Any) -> Optional[int]:
        """Safely convert value to integer"""
        if value is None or value == '' or value == 'N/A':
            return None
        try:
            return int(float(str(value)))
        except (ValueError, TypeError):
            return None
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """Safely convert value to float"""
        if value is None or value == '' or value == 'N/A':
            return None
        try:
            # Handle percentage strings
            if isinstance(value, str) and '%' in value:
                return float(value.replace('%', ''))
            return float(str(value))
        except (ValueError, TypeError):
            return None
    
    def get_events_list(self) -> pd.DataFrame:
        """Get list of all events in database"""
        with sqlite3.connect(self.db_path) as conn:
            query = '''
                SELECT event_id, title, dates, location, prize_pool, 
                       scraped_at, created_at
                FROM events 
                ORDER BY created_at DESC
            '''
            return pd.read_sql_query(query, conn)
    
    def get_event_data(self, event_id: str) -> Dict[str, pd.DataFrame]:
        """Get all data for a specific event"""
        with sqlite3.connect(self.db_path) as conn:
            data = {}
            
            # Event info
            event_query = 'SELECT * FROM events WHERE event_id = ?'
            data['event_info'] = pd.read_sql_query(event_query, conn, params=(event_id,))
            
            # Matches
            matches_query = 'SELECT * FROM matches WHERE event_id = ? ORDER BY created_at'
            data['matches'] = pd.read_sql_query(matches_query, conn, params=(event_id,))
            
            # Player stats
            stats_query = 'SELECT * FROM player_stats WHERE event_id = ? ORDER BY acs DESC'
            data['player_stats'] = pd.read_sql_query(stats_query, conn, params=(event_id,))
            
            # Agent usage
            agents_query = 'SELECT * FROM agent_usage WHERE event_id = ? ORDER BY usage_count DESC'
            data['agent_usage'] = pd.read_sql_query(agents_query, conn, params=(event_id,))
            
            return data
    
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # Count events
            cursor.execute('SELECT COUNT(*) FROM events')
            stats['total_events'] = cursor.fetchone()[0]
            
            # Count matches
            cursor.execute('SELECT COUNT(*) FROM matches')
            stats['total_matches'] = cursor.fetchone()[0]
            
            # Count player records
            cursor.execute('SELECT COUNT(*) FROM player_stats')
            stats['total_player_records'] = cursor.fetchone()[0]
            
            # Count agent records
            cursor.execute('SELECT COUNT(*) FROM agent_usage')
            stats['total_agent_records'] = cursor.fetchone()[0]
            
            # Count unique players
            cursor.execute('SELECT COUNT(DISTINCT player) FROM player_stats')
            stats['unique_players'] = cursor.fetchone()[0]
            
            # Count unique teams
            cursor.execute('SELECT COUNT(DISTINCT team1) FROM matches')
            stats['unique_teams'] = cursor.fetchone()[0]
            
            return stats
    
    def export_to_csv(self, event_id: str, output_dir: str = "exports") -> List[str]:
        """Export event data to CSV files"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        data = self.get_event_data(event_id)
        files_created = []
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for table_name, df in data.items():
            if not df.empty:
                filename = f"{output_dir}/{table_name}_{event_id}_{timestamp}.csv"
                df.to_csv(filename, index=False)
                files_created.append(filename)
        
        return files_created
    
    def delete_event(self, event_id: str) -> bool:
        """Delete all data for a specific event"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete in reverse order of dependencies
                cursor.execute('DELETE FROM agent_usage WHERE event_id = ?', (event_id,))
                cursor.execute('DELETE FROM player_stats WHERE event_id = ?', (event_id,))
                cursor.execute('DELETE FROM matches WHERE event_id = ?', (event_id,))
                cursor.execute('DELETE FROM events WHERE event_id = ?', (event_id,))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error deleting event {event_id}: {e}")
            return False
    
    def get_database_size(self) -> str:
        """Get database file size in human readable format"""
        try:
            size_bytes = os.path.getsize(self.db_path)
            
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size_bytes < 1024.0:
                    return f"{size_bytes:.1f} {unit}"
                size_bytes /= 1024.0
            
            return f"{size_bytes:.1f} TB"
            
        except OSError:
            return "Unknown"


# Example usage and testing
if __name__ == "__main__":
    # Initialize database
    db = VLRDatabase("test_vlr_data.db")
    
    print("🗄️ VLR Database Manager Test")
    print("=" * 40)
    
    # Test database initialization
    print("✅ Database initialized successfully")
    
    # Get database stats
    stats = db.get_database_stats()
    print(f"📊 Database Stats:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Get database size
    size = db.get_database_size()
    print(f"💾 Database size: {size}")
    
    # List events
    events_df = db.get_events_list()
    print(f"📋 Events in database: {len(events_df)}")
    
    print("\n✅ Database manager is working correctly!")
