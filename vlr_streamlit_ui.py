import streamlit as st
import pandas as pd
import json
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from vlr_comprehensive_scraper import VLRComprehensiveScraper
from vlr_database import VLRDatabase

# Configure Streamlit page
st.set_page_config(
    page_title="VLR.gg Comprehensive Scraper",
    page_icon="🎮",
    layout="wide",
    initial_sidebar_state="expanded"
)

def init_session_state():
    """Initialize session state variables"""
    if 'scraped_data' not in st.session_state:
        st.session_state.scraped_data = None
    if 'scraping_progress' not in st.session_state:
        st.session_state.scraping_progress = 0
    if 'scraping_status' not in st.session_state:
        st.session_state.scraping_status = "Ready to scrape..."
    if 'current_step' not in st.session_state:
        st.session_state.current_step = "idle"
    if 'db' not in st.session_state:
        st.session_state.db = VLRDatabase()
    if 'show_data_preview' not in st.session_state:
        st.session_state.show_data_preview = False

def display_header():
    """Display the main header"""
    st.title("🎮 VLR.gg Comprehensive Event Scraper")
    st.markdown("""
    ### Extract comprehensive data from VLR.gg tournament events

    This tool scrapes data from three main tabs:
    - **🏆 Matches**: Match details, scores, and series information
    - **📊 Stats**: Player statistics for the entire event
    - **🎭 Agents**: Agent usage statistics and percentages
    """)
    st.divider()

def display_url_input():
    """Display URL input section"""
    st.header("📝 Event URL Input")

    col1, col2 = st.columns([4, 1])

    with col1:
        url = st.text_input(
            "VLR.gg Event URL:",
            placeholder="https://www.vlr.gg/event/2097/valorant-champions-2024",
            help="Enter the main VLR.gg event URL. The scraper will automatically access the matches, stats, and agents tabs.",
            key="main_url"
        )

    with col2:
        st.write("")  # Spacing
        validate_clicked = st.button("🔍 Validate", type="secondary")

    # URL validation
    if validate_clicked and url:
        scraper = VLRComprehensiveScraper()
        is_valid, message = scraper.validate_url(url)

        if is_valid:
            st.success(f"✅ {message}")

            # Show constructed URLs
            try:
                urls = scraper.construct_tab_urls(url)
                st.info("📋 **URLs that will be scraped:**")
                st.write(f"🏆 **Matches**: {urls['matches']}")
                st.write(f"📊 **Stats**: {urls['stats']}")
                st.write(f"🎭 **Agents**: {urls['agents']}")
            except Exception as e:
                st.warning(f"Could not construct URLs: {e}")
        else:
            st.error(f"❌ {message}")
    elif validate_clicked:
        st.warning("Please enter a URL first")

    return url

def display_control_panel(url):
    """Display control panel with scraping options"""
    st.header("🎛️ Control Panel")

    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        # Scraping options
        with st.expander("⚙️ Scraping Options", expanded=True):
            scrape_matches = st.checkbox("🏆 Scrape Matches Data", value=True,
                                       help="Extract match details and scores")
            scrape_stats = st.checkbox("📊 Scrape Player Statistics", value=True,
                                     help="Extract player stats from the event")
            scrape_agents = st.checkbox("🎭 Scrape Agent Usage Data", value=True,
                                      help="Extract agent usage statistics")

            save_separate = st.checkbox("📁 Save Separate Files", value=True,
                                      help="Save each data type in separate JSON files")

    with col2:
        st.write("")  # Spacing
        scrape_clicked = st.button("🚀 Start Scraping", type="primary",
                                 disabled=not url or st.session_state.current_step == "scraping")

    with col3:
        st.write("")  # Spacing
        if st.button("🗑️ Clear Data", type="secondary"):
            st.session_state.scraped_data = None
            st.session_state.scraping_progress = 0
            st.session_state.scraping_status = "Data cleared - Ready to scrape..."
            st.session_state.current_step = "idle"
            st.rerun()

    return scrape_clicked, scrape_matches, scrape_stats, scrape_agents, save_separate

def display_progress():
    """Display scraping progress"""
    st.header("📊 Scraping Progress")

    # Progress bar
    progress_bar = st.progress(st.session_state.scraping_progress / 100)

    # Status text
    status_container = st.container()
    with status_container:
        st.text(st.session_state.scraping_status)

    # Statistics if data exists
    if st.session_state.scraped_data:
        data = st.session_state.scraped_data

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            matches_count = data.get('matches_data', {}).get('total_matches', 0)
            st.metric("🏆 Matches", matches_count)

        with col2:
            players_count = data.get('stats_data', {}).get('total_players', 0)
            st.metric("👤 Players", players_count)

        with col3:
            agents_count = data.get('agents_data', {}).get('total_agents', 0)
            st.metric("🎭 Agents", agents_count)

        with col4:
            # Calculate unique teams from matches
            matches = data.get('matches_data', {}).get('matches', [])
            if matches:
                teams = set()
                for match in matches:
                    teams.add(match.get('team1', ''))
                    teams.add(match.get('team2', ''))
                teams.discard('')  # Remove empty strings
                st.metric("🏅 Teams", len(teams))
            else:
                st.metric("🏅 Teams", 0)

    return status_container

def perform_scraping(url, scrape_matches, scrape_stats, scrape_agents, status_container):
    """Perform the actual scraping"""
    try:
        st.session_state.current_step = "scraping"
        scraper = VLRComprehensiveScraper()

        # Progress callback function
        def update_progress(message):
            st.session_state.scraping_status = message
            with status_container:
                st.text(message)

        # Validate URL first
        is_valid, message = scraper.validate_url(url)
        if not is_valid:
            st.error(f"❌ {message}")
            st.session_state.current_step = "idle"
            return

        # Initialize progress
        st.session_state.scraping_progress = 10
        update_progress("Initializing scraper...")

        # Get URLs
        urls = scraper.construct_tab_urls(url)
        st.session_state.scraping_progress = 20

        # Initialize result
        result = {
            'event_info': {},
            'matches_data': {},
            'stats_data': {},
            'agents_data': {},
            'urls': urls,
            'scraped_at': datetime.now().isoformat()
        }

        # Scrape event info
        update_progress("Scraping event information...")
        st.session_state.scraping_progress = 30
        result['event_info'] = scraper.scrape_event_info(url)

        # Scrape matches if requested
        if scrape_matches:
            update_progress("Scraping matches data...")
            st.session_state.scraping_progress = 50
            result['matches_data'] = scraper.scrape_matches_tab(urls['matches'], update_progress)

        # Scrape stats if requested
        if scrape_stats:
            update_progress("Scraping player statistics...")
            st.session_state.scraping_progress = 70
            result['stats_data'] = scraper.scrape_stats_tab(urls['stats'], update_progress)

        # Scrape agents if requested
        if scrape_agents:
            update_progress("Scraping agent usage data...")
            st.session_state.scraping_progress = 90
            result['agents_data'] = scraper.scrape_agents_tab(urls['agents'], update_progress)

        # Complete
        st.session_state.scraping_progress = 100
        st.session_state.scraping_status = "✅ Scraping completed successfully!"
        st.session_state.scraped_data = result
        st.session_state.current_step = "completed"

        st.success("✅ Data scraped successfully!")
        st.rerun()

    except Exception as e:
        st.session_state.scraping_status = f"❌ Error: {str(e)}"
        st.session_state.current_step = "error"
        st.error(f"❌ Error during scraping: {str(e)}")

def display_event_info(event_info):
    """Display event information"""
    with st.container(border=True):
        col1, col2 = st.columns(2)

        with col1:
            if 'title' in event_info:
                st.metric("📋 Event Title", event_info['title'])
            if 'dates' in event_info:
                st.metric("📅 Dates", event_info['dates'])

        with col2:
            if 'location' in event_info:
                st.metric("📍 Location", event_info['location'])
            if 'prize_pool' in event_info:
                st.metric("💰 Prize Pool", event_info['prize_pool'])

        if 'description' in event_info:
            st.text_area("📝 Description", event_info['description'], height=100, disabled=True)

        st.text(f"🔗 URL: {event_info['url']}")
        st.text(f"🕒 Scraped: {event_info['scraped_at']}")

def display_matches_data(matches_data):
    """Display matches data with enhanced visualizations"""
    matches = matches_data.get('matches', [])

    if not matches:
        st.warning("No matches data found")
        return

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("📊 Total Matches", len(matches))

    with col2:
        completed = len([m for m in matches if m.get('status') == 'Completed'])
        st.metric("✅ Completed", completed)

    with col3:
        scheduled = len([m for m in matches if m.get('status') == 'Scheduled'])
        st.metric("⏰ Scheduled", scheduled)

    with col4:
        teams = set()
        for match in matches:
            teams.add(match.get('team1', ''))
            teams.add(match.get('team2', ''))
        teams.discard('')
        st.metric("🏅 Teams", len(teams))

    # Convert to DataFrame for analysis
    matches_df = pd.DataFrame(matches)

    if not matches_df.empty:
        # Matches by stage visualization
        if 'stage' in matches_df.columns:
            st.subheader("📊 Matches by Stage")
            stage_counts = matches_df['stage'].value_counts()

            fig_stages = px.bar(
                x=stage_counts.index,
                y=stage_counts.values,
                title="Number of Matches by Stage",
                labels={'x': 'Stage', 'y': 'Number of Matches'}
            )
            fig_stages.update_layout(xaxis_tickangle=-45)
            st.plotly_chart(fig_stages, use_container_width=True)

        # Team performance analysis for completed matches
        completed_matches = matches_df[matches_df['status'] == 'Completed'].copy()
        if not completed_matches.empty and 'winner' in completed_matches.columns:
            st.subheader("🏆 Team Performance")

            # Calculate wins for each team
            team_wins = {}
            team_matches = {}

            for _, match in completed_matches.iterrows():
                team1, team2 = match.get('team1', ''), match.get('team2', '')
                winner = match.get('winner', '')

                # Initialize teams
                for team in [team1, team2]:
                    if team and team != '':
                        if team not in team_wins:
                            team_wins[team] = 0
                            team_matches[team] = 0
                        team_matches[team] += 1

                # Count wins
                if winner and winner in team_wins:
                    team_wins[winner] += 1

            # Create performance DataFrame
            if team_wins:
                perf_data = []
                for team in team_wins:
                    wins = team_wins[team]
                    total = team_matches[team]
                    win_rate = (wins / total * 100) if total > 0 else 0
                    perf_data.append({
                        'Team': team,
                        'Wins': wins,
                        'Total Matches': total,
                        'Losses': total - wins,
                        'Win Rate (%)': round(win_rate, 1)
                    })

                perf_df = pd.DataFrame(perf_data).sort_values('Win Rate (%)', ascending=False)

                # Display top teams
                col1, col2 = st.columns(2)

                with col1:
                    st.write("**🥇 Top Performing Teams:**")
                    st.dataframe(perf_df.head(10), hide_index=True)

                with col2:
                    # Win rate chart
                    if len(perf_df) > 0:
                        fig_winrate = px.bar(
                            perf_df.head(10),
                            x='Team',
                            y='Win Rate (%)',
                            title="Team Win Rates",
                            color='Win Rate (%)',
                            color_continuous_scale='RdYlGn'
                        )
                        fig_winrate.update_layout(xaxis_tickangle=-45)
                        st.plotly_chart(fig_winrate, use_container_width=True)

        # Matches table with filtering
        st.subheader("📋 Matches Table")

        # Add filters
        col1, col2, col3 = st.columns(3)

        with col1:
            status_filter = st.selectbox(
                "Filter by Status:",
                options=['All'] + list(matches_df['status'].unique()),
                key="matches_status_filter"
            )

        with col2:
            if 'stage' in matches_df.columns:
                stage_filter = st.selectbox(
                    "Filter by Stage:",
                    options=['All'] + list(matches_df['stage'].unique()),
                    key="matches_stage_filter"
                )
            else:
                stage_filter = 'All'

        with col3:
            # Team search
            team_search = st.text_input(
                "Search Team:",
                placeholder="Enter team name...",
                key="matches_team_search"
            )

        # Apply filters
        filtered_df = matches_df.copy()

        if status_filter != 'All':
            filtered_df = filtered_df[filtered_df['status'] == status_filter]

        if stage_filter != 'All' and 'stage' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['stage'] == stage_filter]

        if team_search:
            team_mask = (
                filtered_df['team1'].str.contains(team_search, case=False, na=False) |
                filtered_df['team2'].str.contains(team_search, case=False, na=False)
            )
            filtered_df = filtered_df[team_mask]

        # Display filtered table
        display_columns = ['team1', 'score1', 'score2', 'team2', 'stage', 'time', 'status']
        if 'winner' in filtered_df.columns:
            display_columns.append('winner')

        available_columns = [col for col in display_columns if col in filtered_df.columns]

        st.dataframe(
            filtered_df[available_columns],
            use_container_width=True,
            hide_index=True
        )

        st.info(f"Showing {len(filtered_df)} of {len(matches_df)} matches")

def display_stats_data(stats_data):
    """Display player statistics data with enhanced visualizations"""
    player_stats = stats_data.get('player_stats', [])

    if not player_stats:
        st.warning("No player statistics found")
        return

    # Convert to DataFrame
    stats_df = pd.DataFrame(player_stats)

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("👥 Total Players", len(player_stats))

    with col2:
        unique_teams = len(set([p.get('team', '') for p in player_stats if p.get('team')]))
        st.metric("🏅 Teams", unique_teams)

    with col3:
        # Average ACS
        if 'acs' in stats_df.columns:
            avg_acs = pd.to_numeric(stats_df['acs'], errors='coerce').mean()
            st.metric("📊 Avg ACS", f"{avg_acs:.1f}" if not pd.isna(avg_acs) else "N/A")
        else:
            st.metric("📊 Data Points", len(player_stats))

    with col4:
        # Average K/D
        if 'kd_ratio' in stats_df.columns:
            kd_numeric = pd.to_numeric(stats_df['kd_ratio'], errors='coerce')
            avg_kd = kd_numeric.mean()
            st.metric("⚔️ Avg K/D", f"{avg_kd:.2f}" if not pd.isna(avg_kd) else "N/A")
        else:
            st.metric("📈 Records", len(player_stats))

    # Convert numeric columns for analysis
    numeric_cols = ['rating', 'acs', 'kills', 'deaths', 'assists', 'adr', 'kd_ratio']
    for col in numeric_cols:
        if col in stats_df.columns:
            stats_df[col] = pd.to_numeric(stats_df[col], errors='coerce')

    # Top performers section
    st.subheader("🌟 Top Performers")

    # Create tabs for different metrics
    perf_tab1, perf_tab2, perf_tab3, perf_tab4 = st.tabs(["🎯 ACS Leaders", "⚔️ K/D Leaders", "🔥 Fraggers", "🎖️ Rating Leaders"])

    with perf_tab1:
        if 'acs' in stats_df.columns:
            top_acs = stats_df.nlargest(15, 'acs')[['player', 'team', 'acs', 'kills', 'deaths', 'kd_ratio']]

            col1, col2 = st.columns(2)
            with col1:
                st.dataframe(top_acs.head(10), hide_index=True)

            with col2:
                # ACS distribution chart
                fig_acs = px.bar(
                    top_acs.head(10),
                    x='player',
                    y='acs',
                    title="Top 10 Players by ACS",
                    color='acs',
                    color_continuous_scale='viridis'
                )
                fig_acs.update_layout(xaxis_tickangle=-45)
                st.plotly_chart(fig_acs, use_container_width=True)

    with perf_tab2:
        if 'kd_ratio' in stats_df.columns:
            # Filter out infinite values for display
            kd_filtered = stats_df[stats_df['kd_ratio'] != float('inf')].copy()
            top_kd = kd_filtered.nlargest(15, 'kd_ratio')[['player', 'team', 'kd_ratio', 'kills', 'deaths', 'acs']]

            col1, col2 = st.columns(2)
            with col1:
                st.dataframe(top_kd.head(10), hide_index=True)

            with col2:
                # K/D scatter plot
                if len(top_kd) > 0:
                    fig_kd = px.scatter(
                        top_kd.head(15),
                        x='kills',
                        y='deaths',
                        size='kd_ratio',
                        color='kd_ratio',
                        hover_data=['player', 'team'],
                        title="Kills vs Deaths (Size = K/D Ratio)",
                        color_continuous_scale='RdYlGn'
                    )
                    st.plotly_chart(fig_kd, use_container_width=True)

    with perf_tab3:
        if 'kills' in stats_df.columns:
            top_kills = stats_df.nlargest(15, 'kills')[['player', 'team', 'kills', 'deaths', 'acs', 'kd_ratio']]

            col1, col2 = st.columns(2)
            with col1:
                st.dataframe(top_kills.head(10), hide_index=True)

            with col2:
                # Kills distribution
                fig_kills = px.histogram(
                    stats_df,
                    x='kills',
                    nbins=20,
                    title="Distribution of Kills",
                    labels={'count': 'Number of Players'}
                )
                st.plotly_chart(fig_kills, use_container_width=True)

    with perf_tab4:
        if 'rating' in stats_df.columns:
            top_rating = stats_df.nlargest(15, 'rating')[['player', 'team', 'rating', 'acs', 'kills', 'kd_ratio']]

            col1, col2 = st.columns(2)
            with col1:
                st.dataframe(top_rating.head(10), hide_index=True)

            with col2:
                # Rating vs ACS correlation
                if len(stats_df) > 0:
                    fig_rating = px.scatter(
                        stats_df.head(50),
                        x='rating',
                        y='acs',
                        hover_data=['player', 'team'],
                        title="Rating vs ACS Correlation",
                        trendline="ols"
                    )
                    st.plotly_chart(fig_rating, use_container_width=True)

    # Team analysis
    if 'team' in stats_df.columns and stats_df['team'].notna().any():
        st.subheader("🏅 Team Analysis")

        # Calculate team averages
        team_stats = stats_df.groupby('team').agg({
            'acs': 'mean',
            'kd_ratio': 'mean',
            'kills': 'mean',
            'rating': 'mean'
        }).round(2)

        team_stats['player_count'] = stats_df.groupby('team').size()
        team_stats = team_stats.sort_values('acs', ascending=False)

        col1, col2 = st.columns(2)

        with col1:
            st.write("**📊 Team Performance Summary:**")
            st.dataframe(team_stats, use_container_width=True)

        with col2:
            # Team ACS comparison
            if len(team_stats) > 0:
                fig_team = px.bar(
                    team_stats.head(10),
                    y=team_stats.head(10).index,
                    x='acs',
                    orientation='h',
                    title="Team Average ACS",
                    color='acs',
                    color_continuous_scale='viridis'
                )
                st.plotly_chart(fig_team, use_container_width=True)

    # Player search and filtering
    st.subheader("🔍 Player Search & Filter")

    col1, col2, col3 = st.columns(3)

    with col1:
        player_search = st.text_input(
            "Search Player:",
            placeholder="Enter player name...",
            key="player_search"
        )

    with col2:
        if 'team' in stats_df.columns:
            team_filter = st.selectbox(
                "Filter by Team:",
                options=['All'] + sorted(stats_df['team'].dropna().unique().tolist()),
                key="player_team_filter"
            )
        else:
            team_filter = 'All'

    with col3:
        # Sort options
        sort_options = ['acs', 'rating', 'kills', 'kd_ratio', 'adr']
        available_sort = [opt for opt in sort_options if opt in stats_df.columns]

        if available_sort:
            sort_by = st.selectbox(
                "Sort by:",
                options=available_sort,
                key="player_sort"
            )
        else:
            sort_by = None

    # Apply filters
    filtered_stats = stats_df.copy()

    if player_search:
        filtered_stats = filtered_stats[
            filtered_stats['player'].str.contains(player_search, case=False, na=False)
        ]

    if team_filter != 'All' and 'team' in filtered_stats.columns:
        filtered_stats = filtered_stats[filtered_stats['team'] == team_filter]

    if sort_by and sort_by in filtered_stats.columns:
        filtered_stats = filtered_stats.sort_values(sort_by, ascending=False)

    # Full stats table
    st.subheader("📊 Player Statistics Table")
    display_cols = ['player', 'team', 'rating', 'acs', 'kills', 'deaths', 'assists', 'kd_ratio', 'adr', 'hs_percent']
    available_cols = [col for col in display_cols if col in filtered_stats.columns]

    st.dataframe(
        filtered_stats[available_cols],
        use_container_width=True,
        hide_index=True
    )

    st.info(f"Showing {len(filtered_stats)} of {len(stats_df)} players")

def display_agents_data(agents_data):
    """Display agent usage data"""
    agent_stats = agents_data.get('agent_stats', [])

    if not agent_stats:
        st.warning("No agent usage data found")
        return

    # Convert to DataFrame
    agents_df = pd.DataFrame(agent_stats)

    # Summary metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("🎭 Total Agents", len(agent_stats))

    with col2:
        total_usage = sum([int(a.get('usage_count', '0')) for a in agent_stats if a.get('usage_count', '0').isdigit()])
        st.metric("📊 Total Picks", total_usage)

    with col3:
        st.metric("📈 Data Points", len(agent_stats))

    # Agent usage chart
    if 'usage_percentage' in agents_df.columns:
        st.subheader("📊 Agent Usage Distribution")

        # Clean percentage data
        agents_df['usage_pct_clean'] = agents_df['usage_percentage'].str.replace('%', '').astype(float, errors='ignore')

        # Create bar chart
        fig = px.bar(
            agents_df.head(15),  # Top 15 agents
            x='agent',
            y='usage_pct_clean',
            title='Top Agent Usage Percentages',
            labels={'usage_pct_clean': 'Usage Percentage (%)', 'agent': 'Agent'}
        )
        fig.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig, use_container_width=True)

    # Full agents table
    st.subheader("🎭 Agent Usage Statistics")
    display_cols = ['agent', 'usage_count', 'usage_percentage', 'win_rate', 'avg_rating', 'avg_acs']
    available_cols = [col for col in display_cols if col in agents_df.columns]

    st.dataframe(
        agents_df[available_cols],
        use_container_width=True,
        hide_index=True
    )

def display_results():
    """Display scraped results in tabs"""
    if not st.session_state.scraped_data:
        return

    st.header("📊 Scraped Data Results")
    data = st.session_state.scraped_data

    # Create tabs for different data types
    tab1, tab2, tab3, tab4 = st.tabs(["ℹ️ Event Info", "🏆 Matches", "📊 Player Stats", "🎭 Agent Usage"])

    with tab1:
        st.subheader("Event Information")
        display_event_info(data.get('event_info', {}))

    with tab2:
        st.subheader("Matches Data")
        display_matches_data(data.get('matches_data', {}))

    with tab3:
        st.subheader("Player Statistics")
        display_stats_data(data.get('stats_data', {}))

    with tab4:
        st.subheader("Agent Usage Statistics")
        display_agents_data(data.get('agents_data', {}))

def display_database_section():
    """Display database management section"""
    st.header("🗄️ Database Management")

    # Database stats
    db_stats = st.session_state.db.get_database_stats()
    db_size = st.session_state.db.get_database_size()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("📊 Events", db_stats.get('total_events', 0))

    with col2:
        st.metric("🏆 Matches", db_stats.get('total_matches', 0))

    with col3:
        st.metric("👥 Players", db_stats.get('unique_players', 0))

    with col4:
        st.metric("💾 DB Size", db_size)

    # Save to database section
    if st.session_state.scraped_data:
        st.subheader("💾 Save Current Data to Database")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🗄️ Save to Database", type="primary"):
                try:
                    event_id = st.session_state.db.save_comprehensive_data(st.session_state.scraped_data)
                    st.success(f"✅ Data saved to database! Event ID: {event_id}")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Error saving to database: {e}")

        with col2:
            st.info("💡 **Tip**: Save your scraped data to the database for persistent storage and easy retrieval later.")

    # View saved events
    st.subheader("📋 Saved Events")

    try:
        events_df = st.session_state.db.get_events_list()

        if not events_df.empty:
            # Display events table
            st.dataframe(
                events_df[['event_id', 'title', 'dates', 'location', 'scraped_at']],
                use_container_width=True,
                hide_index=True
            )

            # Event management
            col1, col2, col3 = st.columns(3)

            with col1:
                selected_event = st.selectbox(
                    "Select Event to View:",
                    options=events_df['event_id'].tolist(),
                    format_func=lambda x: f"{x} - {events_df[events_df['event_id']==x]['title'].iloc[0] if len(events_df[events_df['event_id']==x]) > 0 else 'Unknown'}",
                    key="db_event_select"
                )

            with col2:
                if st.button("👁️ View Event Data"):
                    if selected_event:
                        try:
                            event_data = st.session_state.db.get_event_data(selected_event)
                            st.session_state.db_event_data = event_data
                            st.session_state.show_db_event = True
                            st.rerun()
                        except Exception as e:
                            st.error(f"Error loading event data: {e}")

            with col3:
                if st.button("🗑️ Delete Event", type="secondary"):
                    if selected_event:
                        if st.session_state.db.delete_event(selected_event):
                            st.success(f"Event {selected_event} deleted successfully!")
                            st.rerun()
                        else:
                            st.error("Failed to delete event")
        else:
            st.info("No events saved in database yet. Scrape some data and save it to get started!")

    except Exception as e:
        st.error(f"Error accessing database: {e}")

def display_database_event_data():
    """Display event data from database"""
    if not hasattr(st.session_state, 'db_event_data') or not st.session_state.get('show_db_event', False):
        return

    st.header("📊 Database Event Data")

    event_data = st.session_state.db_event_data

    # Close button
    if st.button("❌ Close Database View"):
        st.session_state.show_db_event = False
        st.rerun()

    # Create tabs for database data
    tab1, tab2, tab3, tab4 = st.tabs(["ℹ️ Event Info", "🏆 Matches", "📊 Player Stats", "🎭 Agent Usage"])

    with tab1:
        if not event_data['event_info'].empty:
            event_info = event_data['event_info'].iloc[0]

            col1, col2 = st.columns(2)
            with col1:
                st.metric("📋 Title", event_info.get('title', 'N/A'))
                st.metric("📅 Dates", event_info.get('dates', 'N/A'))

            with col2:
                st.metric("📍 Location", event_info.get('location', 'N/A'))
                st.metric("💰 Prize Pool", event_info.get('prize_pool', 'N/A'))
        else:
            st.warning("No event information found")

    with tab2:
        if not event_data['matches'].empty:
            matches_df = event_data['matches']

            # Summary
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("📊 Total Matches", len(matches_df))
            with col2:
                completed = len(matches_df[matches_df['status'] == 'Completed'])
                st.metric("✅ Completed", completed)
            with col3:
                scheduled = len(matches_df[matches_df['status'] == 'Scheduled'])
                st.metric("⏰ Scheduled", scheduled)

            # Matches table
            display_cols = ['team1', 'score1', 'score2', 'team2', 'stage', 'status']
            available_cols = [col for col in display_cols if col in matches_df.columns]

            st.dataframe(
                matches_df[available_cols],
                use_container_width=True,
                hide_index=True
            )
        else:
            st.warning("No matches data found")

    with tab3:
        if not event_data['player_stats'].empty:
            stats_df = event_data['player_stats']

            # Summary
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("👥 Total Players", len(stats_df))
            with col2:
                unique_teams = stats_df['team'].nunique()
                st.metric("🏅 Teams", unique_teams)
            with col3:
                avg_acs = stats_df['acs'].mean() if 'acs' in stats_df.columns else 0
                st.metric("📊 Avg ACS", f"{avg_acs:.1f}")

            # Top performers
            if 'acs' in stats_df.columns:
                top_players = stats_df.nlargest(10, 'acs')[['player', 'team', 'acs', 'kills', 'deaths', 'kd_ratio']]
                st.subheader("🌟 Top Performers")
                st.dataframe(top_players, hide_index=True)

            # Full table
            st.subheader("📊 All Player Statistics")
            display_cols = ['player', 'team', 'rating', 'acs', 'kills', 'deaths', 'kd_ratio']
            available_cols = [col for col in display_cols if col in stats_df.columns]

            st.dataframe(
                stats_df[available_cols],
                use_container_width=True,
                hide_index=True
            )
        else:
            st.warning("No player statistics found")

    with tab4:
        if not event_data['agent_usage'].empty:
            agents_df = event_data['agent_usage']

            # Summary
            col1, col2 = st.columns(2)
            with col1:
                st.metric("🎭 Total Agents", len(agents_df))
            with col2:
                total_usage = agents_df['usage_count'].sum() if 'usage_count' in agents_df.columns else 0
                st.metric("📊 Total Picks", total_usage)

            # Agent usage chart
            if 'usage_percentage' in agents_df.columns:
                # Clean percentage data
                agents_df['usage_pct_clean'] = pd.to_numeric(
                    agents_df['usage_percentage'].str.replace('%', ''),
                    errors='coerce'
                )

                fig = px.bar(
                    agents_df.head(10),
                    x='agent',
                    y='usage_pct_clean',
                    title='Agent Usage Distribution',
                    labels={'usage_pct_clean': 'Usage Percentage (%)', 'agent': 'Agent'}
                )
                fig.update_layout(xaxis_tickangle=-45)
                st.plotly_chart(fig, use_container_width=True)

            # Full table
            st.subheader("🎭 Agent Usage Statistics")
            display_cols = ['agent', 'usage_count', 'usage_percentage', 'win_rate']
            available_cols = [col for col in display_cols if col in agents_df.columns]

            st.dataframe(
                agents_df[available_cols],
                use_container_width=True,
                hide_index=True
            )
        else:
            st.warning("No agent usage data found")

def display_download_section(save_separate):
    """Display download options with database integration"""
    if not st.session_state.scraped_data:
        return

    st.header("💾 Save & Download Data")

    # Database save section (prominent)
    st.subheader("🗄️ Save to Database")
    col1, col2 = st.columns([2, 1])

    with col1:
        st.info("💡 **Recommended**: Save your data to the SQLite database for persistent storage, easy querying, and future analysis.")

    with col2:
        if st.button("🗄️ Save to Database", type="primary", key="save_db_main"):
            try:
                event_id = st.session_state.db.save_comprehensive_data(st.session_state.scraped_data)
                st.success(f"✅ Data saved to database! Event ID: {event_id}")
                st.balloons()
            except Exception as e:
                st.error(f"❌ Error saving to database: {e}")

    st.divider()

    # Download options
    st.subheader("📥 Download Options")
    data = st.session_state.scraped_data

    col1, col2, col3 = st.columns(3)

    with col1:
        # Complete JSON download
        json_data = json.dumps(data, indent=2, ensure_ascii=False)
        st.download_button(
            label="📄 Download Complete JSON",
            data=json_data,
            file_name=f"vlr_comprehensive_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json",
            help="Download all scraped data in a single JSON file"
        )

    with col2:
        # Matches CSV
        if data.get('matches_data', {}).get('matches'):
            matches_df = pd.DataFrame(data['matches_data']['matches'])
            matches_csv = matches_df.to_csv(index=False)
            st.download_button(
                label="🏆 Download Matches CSV",
                data=matches_csv,
                file_name=f"vlr_matches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                help="Download matches data as CSV"
            )

    with col3:
        # Player stats CSV
        if data.get('stats_data', {}).get('player_stats'):
            stats_df = pd.DataFrame(data['stats_data']['player_stats'])
            stats_csv = stats_df.to_csv(index=False)
            st.download_button(
                label="📊 Download Player Stats CSV",
                data=stats_csv,
                file_name=f"vlr_player_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                help="Download player statistics as CSV"
            )

    # Separate files option
    if save_separate:
        st.divider()
        st.subheader("📁 Separate JSON Files")

        col1, col2, col3 = st.columns(3)

        with col1:
            if data.get('matches_data'):
                matches_json = json.dumps({
                    'event_info': data.get('event_info', {}),
                    'matches_data': data['matches_data']
                }, indent=2, ensure_ascii=False)

                st.download_button(
                    label="🏆 Matches JSON",
                    data=matches_json,
                    file_name=f"matches_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

        with col2:
            if data.get('stats_data'):
                stats_json = json.dumps({
                    'event_info': data.get('event_info', {}),
                    'stats_data': data['stats_data']
                }, indent=2, ensure_ascii=False)

                st.download_button(
                    label="📊 Player Stats JSON",
                    data=stats_json,
                    file_name=f"player_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

        with col3:
            if data.get('agents_data'):
                agents_json = json.dumps({
                    'event_info': data.get('event_info', {}),
                    'agents_data': data['agents_data']
                }, indent=2, ensure_ascii=False)

                st.download_button(
                    label="🎭 Agent Usage JSON",
                    data=agents_json,
                    file_name=f"agent_usage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

def display_example_urls():
    """Display example URLs"""
    st.header("📝 Example URLs")
    st.markdown("""
    Here are some example VLR.gg event URLs you can use:
    """)

    examples = [
        ("Valorant Champions 2024", "https://www.vlr.gg/event/2097/valorant-champions-2024"),
        ("Masters Madrid 2024", "https://www.vlr.gg/event/1921/champions-tour-2024-masters-madrid"),
        ("Masters Shanghai 2024", "https://www.vlr.gg/event/1999/champions-tour-2024-masters-shanghai"),
    ]

    for title, url in examples:
        st.code(f"{title}: {url}")

def main():
    """Main Streamlit application"""
    # Initialize session state
    init_session_state()

    # Sidebar for navigation
    with st.sidebar:
        st.title("🎮 VLR Scraper")

        page = st.radio(
            "Navigate to:",
            ["🔍 Scrape Data", "🗄️ Database", "📊 View Database Event"],
            key="navigation"
        )

        # Database quick stats in sidebar
        st.divider()
        st.subheader("📊 Database Stats")
        try:
            db_stats = st.session_state.db.get_database_stats()
            st.metric("Events", db_stats.get('total_events', 0))
            st.metric("Matches", db_stats.get('total_matches', 0))
            st.metric("Players", db_stats.get('unique_players', 0))
        except:
            st.error("Database connection issue")

    # Main content based on navigation
    if page == "🔍 Scrape Data":
        # Display header
        display_header()

        # URL input section
        url = display_url_input()
        st.divider()

        # Control panel
        scrape_clicked, scrape_matches, scrape_stats, scrape_agents, save_separate = display_control_panel(url)
        st.divider()

        # Progress section
        status_container = display_progress()
        st.divider()

        # Handle scraping
        if scrape_clicked and url:
            perform_scraping(url, scrape_matches, scrape_stats, scrape_agents, status_container)

        # Display results if available
        if st.session_state.scraped_data:
            # Show data preview first
            st.success("🎉 **Data scraped successfully!** Review the data below before saving or downloading.")

            display_results()
            st.divider()

            # Save & Download section (database first)
            display_download_section(save_separate)
            st.divider()

            # Reset button
            if st.button("🔄 Start New Scraping Session", type="secondary"):
                st.session_state.scraped_data = None
                st.session_state.scraping_progress = 0
                st.session_state.scraping_status = "Ready to scrape..."
                st.session_state.current_step = "idle"
                st.rerun()
        else:
            # Show examples when no data
            display_example_urls()

    elif page == "🗄️ Database":
        # Database management page
        display_database_section()

    elif page == "📊 View Database Event":
        # Database event viewer
        display_database_event_data()

        # If no event is being viewed, show database section
        if not st.session_state.get('show_db_event', False):
            st.info("👆 Go to the Database page to select an event to view, or use the navigation above.")
            display_database_section()

if __name__ == "__main__":
    main()
