# VLR.gg Comprehensive Event Scraper with Database Integration

A comprehensive scraping solution for VLR.gg tournament events that extracts data from multiple tabs, provides rich data visualization, and includes SQLite database storage for persistent data management.

## 🚀 Features

### Multi-Tab Scraping
The scraper automatically accesses three main tabs from any VLR.gg event URL:

1. **🏆 Matches Tab** (`/event/matches/{id}/{name}`)
   - Match details and scores
   - Series information
   - Team matchups
   - Match status (Completed/Scheduled)
   - Team performance analysis

2. **📊 Stats Tab** (`/event/stats/{id}/{name}`)
   - Player statistics for the entire event
   - Performance metrics (ACS, K/D, ADR, etc.)
   - Team affiliations
   - Advanced analytics and visualizations

3. **🎭 Agents Tab** (`/event/agents/{id}/{name}`)
   - Agent usage statistics
   - Usage percentages
   - Win rates by agent
   - Interactive charts

### Enhanced Data Visualization
- **Interactive Charts**: Plotly-powered visualizations for better data insights
- **Performance Analytics**: Team win rates, player comparisons, agent meta analysis
- **Filtering & Search**: Advanced filtering options for all data types
- **Data Preview**: Comprehensive data showcase before downloading

### SQLite Database Integration
- **Persistent Storage**: Save scraped data to SQLite database
- **Data Management**: View, query, and manage saved events
- **Easy Retrieval**: Quick access to previously scraped data
- **Database Analytics**: Track scraping history and database statistics

### Clean Architecture
- **Separate scraper module**: All scraping logic in `vlr_comprehensive_scraper.py`
- **Database module**: SQLite operations in `vlr_database.py`
- **UI-focused Streamlit app**: Enhanced interface in `vlr_streamlit_ui.py`
- **Modular design**: Easy to maintain and extend

## 📁 File Structure

```
├── vlr_comprehensive_scraper.py     # Main scraper module
├── vlr_database.py                 # SQLite database operations
├── vlr_streamlit_ui.py             # Enhanced Streamlit UI application
├── test_scraper.py                 # Test suite for the scraper
├── test_database_integration.py    # Database integration tests
├── README_Comprehensive_Scraper.md # This documentation
└── vct_2024_scraper.py             # Legacy scraper (for reference)
```

## 🛠️ Installation

1. **Install required packages**:
```bash
pip install streamlit requests beautifulsoup4 pandas plotly
```

2. **Clone or download the files** to your project directory

3. **Test the installation**:
```bash
python test_database_integration.py
```

## 🎯 Usage

### Using the Enhanced Streamlit UI

1. **Run the Streamlit app**:
```bash
streamlit run vlr_streamlit_ui.py
```

2. **Navigate through the interface**:
   - **🔍 Scrape Data**: Main scraping interface
   - **🗄️ Database**: Manage saved events and database
   - **📊 View Database Event**: View previously saved event data

3. **Scraping Workflow**:
   - Enter a VLR.gg event URL (e.g., `https://www.vlr.gg/event/2097/valorant-champions-2024`)
   - Configure scraping options (matches, stats, agents)
   - Start scraping and monitor progress
   - **Review data** in comprehensive visualizations
   - **Save to database** for persistent storage
   - Download in multiple formats (JSON, CSV)

4. **Database Features**:
   - View database statistics in sidebar
   - Save scraped data to SQLite database
   - Browse and manage saved events
   - View historical data with full visualizations
   - Export data from database

### Using the Scraper Module Directly

```python
from vlr_comprehensive_scraper import VLRComprehensiveScraper

# Initialize scraper
scraper = VLRComprehensiveScraper()

# Main event URL (user provides this)
main_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"

# Scrape all data
data = scraper.scrape_comprehensive_data(main_url)

# Save results
filename = scraper.save_to_json(data)
print(f"Data saved to: {filename}")
```

### Using the Database Module

```python
from vlr_database import VLRDatabase

# Initialize database
db = VLRDatabase("my_vlr_data.db")

# Save scraped data to database
event_id = db.save_comprehensive_data(scraped_data)
print(f"Data saved with event ID: {event_id}")

# Retrieve data from database
events_list = db.get_events_list()
event_data = db.get_event_data(event_id)

# Get database statistics
stats = db.get_database_stats()
print(f"Database contains {stats['total_events']} events")

# Export data to CSV
csv_files = db.export_to_csv(event_id, "exports/")
print(f"Exported {len(csv_files)} CSV files")
```

## 🗄️ Database Features

### Database Schema
The SQLite database includes four main tables:

1. **Events Table**: Event metadata (title, dates, location, etc.)
2. **Matches Table**: Match details, scores, and results
3. **Player Stats Table**: Individual player performance metrics
4. **Agent Usage Table**: Agent pick rates and statistics

### Database Operations
- **Save Data**: Store comprehensive scraped data
- **Retrieve Data**: Query saved events and statistics
- **Export Data**: Export to CSV files
- **Manage Events**: View, delete, and organize saved events
- **Analytics**: Database statistics and insights

### Data Persistence Benefits
- **Historical Analysis**: Compare events over time
- **Offline Access**: View data without re-scraping
- **Data Integrity**: Structured storage with relationships
- **Performance**: Fast queries and filtering
- **Backup**: Easy database backup and sharing

### URL Construction

The scraper automatically constructs the correct URLs:

**Input**: `https://www.vlr.gg/event/2097/valorant-champions-2024`

**Generated URLs**:
- Matches: `https://www.vlr.gg/event/matches/2097/valorant-champions-2024`
- Stats: `https://www.vlr.gg/event/stats/2097/valorant-champions-2024`
- Agents: `https://www.vlr.gg/event/agents/2097/valorant-champions-2024`

## 📊 Data Structure

### Event Info
```json
{
  "title": "VALORANT Champions 2024",
  "dates": "Aug 1 - 25, 2024",
  "location": "Seoul & Incheon, South Korea",
  "prize_pool": "$1,000,000 USD",
  "url": "https://www.vlr.gg/event/2097/valorant-champions-2024"
}
```

### Matches Data
```json
{
  "matches": [
    {
      "team1": "Team A",
      "team2": "Team B",
      "score1": "2",
      "score2": "1",
      "stage": "Grand Final",
      "status": "Completed",
      "winner": "Team A"
    }
  ]
}
```

### Player Stats
```json
{
  "player_stats": [
    {
      "player": "Player Name",
      "team": "Team Name",
      "rating": "1.25",
      "acs": "245",
      "kills": "18",
      "deaths": "12",
      "kd_ratio": "1.50"
    }
  ]
}
```

### Agent Usage
```json
{
  "agent_stats": [
    {
      "agent": "Jett",
      "usage_count": "45",
      "usage_percentage": "23.5%",
      "win_rate": "65%"
    }
  ]
}
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
python test_scraper.py
```

The test suite includes:
- URL construction and validation
- Event info scraping
- Matches data extraction
- Player stats scraping
- Agent usage data collection
- Comprehensive integration test

## 🎮 Example URLs

- **Valorant Champions 2024**: `https://www.vlr.gg/event/2097/valorant-champions-2024`
- **Masters Madrid 2024**: `https://www.vlr.gg/event/1921/champions-tour-2024-masters-madrid`
- **Masters Shanghai 2024**: `https://www.vlr.gg/event/1999/champions-tour-2024-masters-shanghai`

## 🔧 Configuration

### Scraper Settings
- **Request timeout**: 15 seconds
- **Rate limiting**: Small delays between requests
- **User-Agent**: Modern browser simulation
- **Error handling**: Graceful fallbacks

### UI Features
- **Progress tracking**: Real-time scraping progress
- **Data validation**: URL validation before scraping
- **Multiple download formats**: JSON and CSV options
- **Separate file exports**: Individual files for each data type

## 🚨 Important Notes

1. **Respectful scraping**: The scraper includes delays to avoid overwhelming the server
2. **Error handling**: Robust error handling for network issues and page structure changes
3. **Data validation**: Validates URLs and data before processing
4. **Modular design**: Easy to extend for additional data types

## 🔄 Migration from Legacy Scraper

If you're using the old `vct_2024_scraper.py`:

1. **Replace imports**:
   ```python
   # Old
   from vct_2024_scraper import VCT2024Scraper

   # New
   from vlr_comprehensive_scraper import VLRComprehensiveScraper
   ```

2. **Update method calls**:
   ```python
   # Old
   scraper.scrape_all_events()

   # New
   scraper.scrape_comprehensive_data(main_url)
   ```

3. **Use new UI**:
   ```bash
   # Old
   streamlit run vlr_streamlit_app.py

   # New
   streamlit run vlr_streamlit_ui.py
   ```

## 📈 Future Enhancements

- Support for additional VLR.gg data types
- Database integration options
- Advanced data visualization
- Automated scheduling for regular scraping
- API endpoint creation

## 🤝 Contributing

1. Test your changes with `test_scraper.py`
2. Follow the modular architecture
3. Add appropriate error handling
4. Update documentation

## 📄 License

This project is for educational and research purposes. Please respect VLR.gg's terms of service and use responsibly.
