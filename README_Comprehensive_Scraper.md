# VLR.gg Comprehensive Event Scraper

A comprehensive scraping solution for VLR.gg tournament events that extracts data from multiple tabs and provides a clean Streamlit interface.

## 🚀 Features

### Multi-Tab Scraping
The scraper automatically accesses three main tabs from any VLR.gg event URL:

1. **🏆 Matches Tab** (`/event/matches/{id}/{name}`)
   - Match details and scores
   - Series information
   - Team matchups
   - Match status (Completed/Scheduled)

2. **📊 Stats Tab** (`/event/stats/{id}/{name}`)
   - Player statistics for the entire event
   - Performance metrics (ACS, K/D, ADR, etc.)
   - Team affiliations

3. **🎭 Agents Tab** (`/event/agents/{id}/{name}`)
   - Agent usage statistics
   - Usage percentages
   - Win rates by agent

### Clean Architecture
- **Separate scraper module**: All scraping logic in `vlr_comprehensive_scraper.py`
- **UI-focused Streamlit app**: Clean interface in `vlr_streamlit_ui.py`
- **Modular design**: Easy to maintain and extend

## 📁 File Structure

```
├── vlr_comprehensive_scraper.py    # Main scraper module
├── vlr_streamlit_ui.py            # Streamlit UI application
├── test_scraper.py                # Test suite for the scraper
├── README_Comprehensive_Scraper.md # This documentation
└── vct_2024_scraper.py            # Legacy scraper (for reference)
```

## 🛠️ Installation

1. **Install required packages**:
```bash
pip install streamlit requests beautifulsoup4 pandas plotly
```

2. **Clone or download the files** to your project directory

## 🎯 Usage

### Using the Streamlit UI

1. **Run the Streamlit app**:
```bash
streamlit run vlr_streamlit_ui.py
```

2. **Enter a VLR.gg event URL**:
   - Example: `https://www.vlr.gg/event/2097/valorant-champions-2024`
   - The scraper will automatically construct URLs for all tabs

3. **Configure scraping options**:
   - Choose which data types to scrape
   - Select download formats

4. **Start scraping** and monitor progress

5. **View results** in organized tabs

6. **Download data** in multiple formats (JSON, CSV)

### Using the Scraper Module Directly

```python
from vlr_comprehensive_scraper import VLRComprehensiveScraper

# Initialize scraper
scraper = VLRComprehensiveScraper()

# Main event URL (user provides this)
main_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"

# Scrape all data
data = scraper.scrape_comprehensive_data(main_url)

# Save results
filename = scraper.save_to_json(data)
print(f"Data saved to: {filename}")
```

### URL Construction

The scraper automatically constructs the correct URLs:

**Input**: `https://www.vlr.gg/event/2097/valorant-champions-2024`

**Generated URLs**:
- Matches: `https://www.vlr.gg/event/matches/2097/valorant-champions-2024`
- Stats: `https://www.vlr.gg/event/stats/2097/valorant-champions-2024`
- Agents: `https://www.vlr.gg/event/agents/2097/valorant-champions-2024`

## 📊 Data Structure

### Event Info
```json
{
  "title": "VALORANT Champions 2024",
  "dates": "Aug 1 - 25, 2024",
  "location": "Seoul & Incheon, South Korea",
  "prize_pool": "$1,000,000 USD",
  "url": "https://www.vlr.gg/event/2097/valorant-champions-2024"
}
```

### Matches Data
```json
{
  "matches": [
    {
      "team1": "Team A",
      "team2": "Team B",
      "score1": "2",
      "score2": "1",
      "stage": "Grand Final",
      "status": "Completed",
      "winner": "Team A"
    }
  ]
}
```

### Player Stats
```json
{
  "player_stats": [
    {
      "player": "Player Name",
      "team": "Team Name",
      "rating": "1.25",
      "acs": "245",
      "kills": "18",
      "deaths": "12",
      "kd_ratio": "1.50"
    }
  ]
}
```

### Agent Usage
```json
{
  "agent_stats": [
    {
      "agent": "Jett",
      "usage_count": "45",
      "usage_percentage": "23.5%",
      "win_rate": "65%"
    }
  ]
}
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
python test_scraper.py
```

The test suite includes:
- URL construction and validation
- Event info scraping
- Matches data extraction
- Player stats scraping
- Agent usage data collection
- Comprehensive integration test

## 🎮 Example URLs

- **Valorant Champions 2024**: `https://www.vlr.gg/event/2097/valorant-champions-2024`
- **Masters Madrid 2024**: `https://www.vlr.gg/event/1921/champions-tour-2024-masters-madrid`
- **Masters Shanghai 2024**: `https://www.vlr.gg/event/1999/champions-tour-2024-masters-shanghai`

## 🔧 Configuration

### Scraper Settings
- **Request timeout**: 15 seconds
- **Rate limiting**: Small delays between requests
- **User-Agent**: Modern browser simulation
- **Error handling**: Graceful fallbacks

### UI Features
- **Progress tracking**: Real-time scraping progress
- **Data validation**: URL validation before scraping
- **Multiple download formats**: JSON and CSV options
- **Separate file exports**: Individual files for each data type

## 🚨 Important Notes

1. **Respectful scraping**: The scraper includes delays to avoid overwhelming the server
2. **Error handling**: Robust error handling for network issues and page structure changes
3. **Data validation**: Validates URLs and data before processing
4. **Modular design**: Easy to extend for additional data types

## 🔄 Migration from Legacy Scraper

If you're using the old `vct_2024_scraper.py`:

1. **Replace imports**:
   ```python
   # Old
   from vct_2024_scraper import VCT2024Scraper
   
   # New
   from vlr_comprehensive_scraper import VLRComprehensiveScraper
   ```

2. **Update method calls**:
   ```python
   # Old
   scraper.scrape_all_events()
   
   # New
   scraper.scrape_comprehensive_data(main_url)
   ```

3. **Use new UI**:
   ```bash
   # Old
   streamlit run vlr_streamlit_app.py
   
   # New
   streamlit run vlr_streamlit_ui.py
   ```

## 📈 Future Enhancements

- Support for additional VLR.gg data types
- Database integration options
- Advanced data visualization
- Automated scheduling for regular scraping
- API endpoint creation

## 🤝 Contributing

1. Test your changes with `test_scraper.py`
2. Follow the modular architecture
3. Add appropriate error handling
4. Update documentation

## 📄 License

This project is for educational and research purposes. Please respect VLR.gg's terms of service and use responsibly.
