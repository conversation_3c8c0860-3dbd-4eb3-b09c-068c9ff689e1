import requests
from bs4 import BeautifulSoup
import time
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

class MapsAgentsScraper:
    """
    Dedicated scraper for VLR.gg maps and agents data
    Handles agent usage statistics, map pick rates, and meta analysis
    """
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def construct_agents_url(self, main_url: str) -> str:
        """
        Construct agents URL from main event URL
        Example: https://www.vlr.gg/event/2097/valorant-champions-2024
        -> https://www.vlr.gg/event/agents/2097/valorant-champions-2024
        """
        try:
            # Extract event ID
            match = re.search(r'/event/(\d+)/', main_url)
            if not match:
                raise ValueError("Could not extract event ID from URL")
            
            event_id = match.group(1)
            
            # Extract event name from URL
            url_parts = main_url.split('/')
            event_name = url_parts[-1] if url_parts[-1] else url_parts[-2]
            
            agents_url = f"https://www.vlr.gg/event/agents/{event_id}/{event_name}"
            return agents_url
            
        except Exception as e:
            raise ValueError(f"Error constructing agents URL: {e}")
    
    def scrape_maps_and_agents(self, main_url: str, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Scrape maps and agents data from the agents tab
        """
        try:
            agents_url = self.construct_agents_url(main_url)
            
            if progress_callback:
                progress_callback("Fetching maps and agents page...")
            
            response = self.session.get(agents_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            if progress_callback:
                progress_callback("Parsing agents and maps data...")
            
            # Extract agent usage data (first table)
            agent_stats = self._extract_agent_usage(soup, progress_callback)
            
            # Extract map data if available
            map_stats = self._extract_map_data(soup)
            
            # Extract meta analysis
            meta_analysis = self._analyze_meta(agent_stats, map_stats)
            
            result = {
                'agent_stats': agent_stats,
                'map_stats': map_stats,
                'meta_analysis': meta_analysis,
                'total_agents': len(agent_stats),
                'total_maps': len(map_stats),
                'scraped_from': agents_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            if progress_callback:
                progress_callback(f"Completed! Found {len(agent_stats)} agents, {len(map_stats)} maps")
            
            return result
            
        except Exception as e:
            raise Exception(f"Error scraping maps and agents: {e}")
    
    def _extract_agent_usage(self, soup: BeautifulSoup, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """Extract agent usage statistics from the first table"""
        agent_stats = []
        
        # Find the first table (agent usage statistics)
        agents_table = soup.find('table', class_='wf-table-inset')
        if not agents_table:
            # Try alternative selectors
            agents_table = soup.find('table', class_='agents-table')
            if not agents_table:
                agents_table = soup.find('table')
        
        if not agents_table:
            return []
        
        # Get table rows
        rows = agents_table.find_all('tr')
        
        # Skip header row and process data rows
        for i, row in enumerate(rows[1:], 1):
            if progress_callback and i % 10 == 0:
                progress_callback(f"Processing agent {i}/{len(rows)-1}")
            
            agent_data = self._extract_agent_row(row)
            if agent_data:
                agent_stats.append(agent_data)
        
        return agent_stats
    
    def _extract_agent_row(self, row) -> Optional[Dict[str, Any]]:
        """Extract agent statistics from a table row"""
        try:
            cells = row.find_all('td')
            if len(cells) < 3:  # Minimum expected columns
                return None
            
            agent_data = {
                'scraped_at': datetime.now().isoformat()
            }
            
            # Extract agent name (first cell)
            agent_cell = cells[0]
            
            # Try to find agent image first
            agent_img = agent_cell.find('img')
            if agent_img:
                # Get agent name from alt text or src
                agent_name = agent_img.get('alt', '')
                if not agent_name and agent_img.get('src'):
                    src = agent_img.get('src')
                    if '/agents/' in src:
                        agent_name = src.split('/agents/')[-1].split('.')[0].title()
                        agent_name = agent_name.replace('_', ' ').replace('-', ' ')
                agent_data['agent'] = agent_name
            
            # If no image, try text
            if 'agent' not in agent_data or not agent_data['agent']:
                agent_text = agent_cell.get_text(strip=True)
                if agent_text:
                    agent_data['agent'] = agent_text
            
            if not agent_data.get('agent'):
                return None
            
            # Extract usage statistics
            # Common structure: Agent, Usage Count, Usage %, Win Rate, Avg Rating, Avg ACS
            stats_mapping = [
                ('usage_count', 1),
                ('usage_percentage', 2),
                ('win_rate', 3),
                ('avg_rating', 4),
                ('avg_acs', 5),
                ('pick_rate', 6),
                ('ban_rate', 7)
            ]
            
            for stat_name, cell_index in stats_mapping:
                if cell_index < len(cells):
                    value = self._safe_extract_text(cells[cell_index])
                    agent_data[stat_name] = value
            
            # Calculate additional metrics
            try:
                usage_count = int(agent_data.get('usage_count', '0'))
                agent_data['usage_count_numeric'] = usage_count
                
                # Extract percentage value
                usage_pct = agent_data.get('usage_percentage', '0%')
                usage_pct_clean = float(usage_pct.replace('%', ''))
                agent_data['usage_percentage_numeric'] = usage_pct_clean
                
                # Extract win rate
                win_rate = agent_data.get('win_rate', '0%')
                win_rate_clean = float(win_rate.replace('%', ''))
                agent_data['win_rate_numeric'] = win_rate_clean
                
            except (ValueError, TypeError):
                agent_data['usage_count_numeric'] = 0
                agent_data['usage_percentage_numeric'] = 0
                agent_data['win_rate_numeric'] = 0
            
            return agent_data
            
        except Exception as e:
            return None
    
    def _extract_map_data(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract map statistics if available"""
        try:
            map_stats = []
            
            # Look for map-related tables or sections
            map_sections = soup.find_all(['table', 'div'], class_=re.compile(r'map'))
            
            for section in map_sections:
                if section.name == 'table':
                    # Process map table
                    rows = section.find_all('tr')
                    for row in rows[1:]:  # Skip header
                        map_data = self._extract_map_row(row)
                        if map_data:
                            map_stats.append(map_data)
                else:
                    # Process map section
                    map_elements = section.find_all(['div', 'span'], class_=re.compile(r'map.*name'))
                    for elem in map_elements:
                        map_name = elem.get_text(strip=True)
                        if map_name and len(map_name) < 50:  # Reasonable map name length
                            map_stats.append({
                                'map': map_name,
                                'scraped_at': datetime.now().isoformat()
                            })
            
            return map_stats
            
        except Exception:
            return []
    
    def _extract_map_row(self, row) -> Optional[Dict[str, Any]]:
        """Extract map statistics from a table row"""
        try:
            cells = row.find_all('td')
            if len(cells) < 2:
                return None
            
            map_data = {
                'scraped_at': datetime.now().isoformat()
            }
            
            # Extract map name
            map_cell = cells[0]
            map_img = map_cell.find('img')
            if map_img:
                map_name = map_img.get('alt', '')
                if not map_name and map_img.get('src'):
                    src = map_img.get('src')
                    if '/maps/' in src:
                        map_name = src.split('/maps/')[-1].split('.')[0].title()
                map_data['map'] = map_name
            else:
                map_data['map'] = map_cell.get_text(strip=True)
            
            # Extract map statistics
            stats_mapping = [
                ('pick_count', 1),
                ('pick_rate', 2),
                ('win_rate_attack', 3),
                ('win_rate_defense', 4),
                ('avg_rounds', 5)
            ]
            
            for stat_name, cell_index in stats_mapping:
                if cell_index < len(cells):
                    value = self._safe_extract_text(cells[cell_index])
                    map_data[stat_name] = value
            
            return map_data if map_data.get('map') else None
            
        except Exception:
            return None
    
    def _safe_extract_text(self, cell) -> str:
        """Safely extract text from table cell"""
        try:
            text = cell.get_text(strip=True)
            return text if text else '0'
        except:
            return '0'
    
    def _analyze_meta(self, agent_stats: List[Dict[str, Any]], map_stats: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze meta trends from agent and map data"""
        try:
            meta_analysis = {
                'top_agents': [],
                'agent_roles': {},
                'pick_ban_trends': {},
                'map_preferences': []
            }
            
            # Analyze top agents by usage
            sorted_agents = sorted(agent_stats, key=lambda x: x.get('usage_percentage_numeric', 0), reverse=True)
            meta_analysis['top_agents'] = sorted_agents[:10]
            
            # Categorize agents by role (basic categorization)
            role_mapping = {
                'jett': 'Duelist',
                'reyna': 'Duelist',
                'phoenix': 'Duelist',
                'raze': 'Duelist',
                'yoru': 'Duelist',
                'neon': 'Duelist',
                'iso': 'Duelist',
                'sage': 'Sentinel',
                'cypher': 'Sentinel',
                'killjoy': 'Sentinel',
                'chamber': 'Sentinel',
                'deadlock': 'Sentinel',
                'sova': 'Initiator',
                'breach': 'Initiator',
                'skye': 'Initiator',
                'kayo': 'Initiator',
                'fade': 'Initiator',
                'gekko': 'Initiator',
                'omen': 'Controller',
                'brimstone': 'Controller',
                'viper': 'Controller',
                'astra': 'Controller',
                'harbor': 'Controller',
                'clove': 'Controller'
            }
            
            for agent in agent_stats:
                agent_name = agent.get('agent', '').lower()
                role = role_mapping.get(agent_name, 'Unknown')
                
                if role not in meta_analysis['agent_roles']:
                    meta_analysis['agent_roles'][role] = []
                
                meta_analysis['agent_roles'][role].append({
                    'agent': agent.get('agent'),
                    'usage_percentage': agent.get('usage_percentage_numeric', 0),
                    'win_rate': agent.get('win_rate_numeric', 0)
                })
            
            # Sort agents within each role
            for role in meta_analysis['agent_roles']:
                meta_analysis['agent_roles'][role].sort(key=lambda x: x['usage_percentage'], reverse=True)
            
            # Analyze map preferences
            if map_stats:
                sorted_maps = sorted(map_stats, key=lambda x: float(x.get('pick_rate', '0').replace('%', '')), reverse=True)
                meta_analysis['map_preferences'] = sorted_maps[:7]  # Top 7 maps
            
            return meta_analysis
            
        except Exception:
            return {
                'top_agents': [],
                'agent_roles': {},
                'pick_ban_trends': {},
                'map_preferences': []
            }
    
    def get_agent_meta_summary(self, agent_stats: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get a summary of the agent meta"""
        try:
            summary = {
                'most_picked': None,
                'highest_winrate': None,
                'most_contested': None,
                'total_picks': 0
            }
            
            if not agent_stats:
                return summary
            
            # Most picked agent
            most_picked = max(agent_stats, key=lambda x: x.get('usage_count_numeric', 0))
            summary['most_picked'] = {
                'agent': most_picked.get('agent'),
                'usage_count': most_picked.get('usage_count'),
                'usage_percentage': most_picked.get('usage_percentage')
            }
            
            # Highest win rate agent (with minimum usage threshold)
            high_usage_agents = [a for a in agent_stats if a.get('usage_count_numeric', 0) >= 5]
            if high_usage_agents:
                highest_wr = max(high_usage_agents, key=lambda x: x.get('win_rate_numeric', 0))
                summary['highest_winrate'] = {
                    'agent': highest_wr.get('agent'),
                    'win_rate': highest_wr.get('win_rate'),
                    'usage_count': highest_wr.get('usage_count')
                }
            
            # Total picks
            summary['total_picks'] = sum(a.get('usage_count_numeric', 0) for a in agent_stats)
            
            return summary
            
        except Exception:
            return {
                'most_picked': None,
                'highest_winrate': None,
                'most_contested': None,
                'total_picks': 0
            }


# Example usage
if __name__ == "__main__":
    scraper = MapsAgentsScraper()
    
    # Test URL
    test_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
    
    print("🎭 VLR Maps & Agents Scraper Test")
    print("=" * 40)
    
    try:
        def progress_callback(message):
            print(f"📊 {message}")
        
        # Scrape maps and agents
        data = scraper.scrape_maps_and_agents(test_url, progress_callback)
        
        print(f"\n✅ Scraping completed!")
        print(f"   🎭 Total agents: {data['total_agents']}")
        print(f"   🗺️ Total maps: {data['total_maps']}")
        
        # Show sample agent
        if data['agent_stats']:
            sample = data['agent_stats'][0]
            print(f"   🎯 Sample agent: {sample.get('agent', 'N/A')} - {sample.get('usage_percentage', 'N/A')}")
        
        # Show meta summary
        meta_summary = scraper.get_agent_meta_summary(data['agent_stats'])
        if meta_summary['most_picked']:
            most_picked = meta_summary['most_picked']
            print(f"   🏆 Most picked: {most_picked['agent']} ({most_picked['usage_percentage']})")
        
        print(f"   📊 Total agent picks: {meta_summary['total_picks']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
